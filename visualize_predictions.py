#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
独立的预测结果可视化脚本
直接读取CSV文件进行可视化，无需重新训练模型

使用方法:
python visualize_predictions.py
或
python visualize_predictions.py --file LSTM_test_predictions.csv
"""

import pandas as pd
import matplotlib.pyplot as plt
import numpy as np
import argparse
import os
from datetime import datetime, timedelta

# 设置中文字体和样式
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'Arial Unicode MS']
plt.rcParams['axes.unicode_minus'] = False
plt.style.use('default')  # 使用默认样式

class PredictionVisualizer:
    """预测结果可视化器"""
    
    def __init__(self, csv_file=None):
        """
        初始化可视化器
        
        Args:
            csv_file: CSV文件路径，如果为None则自动查找
        """
        self.csv_file = csv_file
        self.df = None
        
    def find_prediction_files(self):
        """自动查找当前目录下的预测CSV文件"""
        prediction_files = []
        for file in os.listdir('.'):
            if file.endswith('_predictions.csv') or file.endswith('_test_predictions.csv'):
                prediction_files.append(file)
        return prediction_files
    
    def load_data(self, csv_file=None):
        """加载预测数据"""
        if csv_file:
            self.csv_file = csv_file
        
        if not self.csv_file:
            # 自动查找预测文件
            files = self.find_prediction_files()
            if not files:
                raise FileNotFoundError("未找到预测CSV文件，请确保文件名包含'_predictions.csv'")
            
            print(f"找到以下预测文件:")
            for i, file in enumerate(files):
                print(f"  {i+1}. {file}")
            
            if len(files) == 1:
                self.csv_file = files[0]
                print(f"自动选择: {self.csv_file}")
            else:
                choice = input(f"请选择文件 (1-{len(files)}): ")
                try:
                    self.csv_file = files[int(choice)-1]
                except (ValueError, IndexError):
                    self.csv_file = files[0]
                    print(f"无效选择，使用默认文件: {self.csv_file}")
        
        # 加载数据
        try:
            self.df = pd.read_csv(self.csv_file)
            print(f"✅ 成功加载数据: {self.csv_file}")
            print(f"   数据形状: {self.df.shape}")
            print(f"   列名: {list(self.df.columns)}")
            
            # 处理时间列
            if 'dateTime' in self.df.columns:
                self.df['dateTime'] = pd.to_datetime(self.df['dateTime'])
            elif 'date' in self.df.columns:
                self.df['date'] = pd.to_datetime(self.df['date'])
                self.df = self.df.rename(columns={'date': 'dateTime'})
            else:
                # 如果没有时间列，创建一个
                print("⚠️ 未找到时间列，将创建默认时间序列")
                start_time = datetime(2025, 7, 16)  # 默认开始时间
                self.df['dateTime'] = [start_time + timedelta(minutes=15*i) for i in range(len(self.df))]
            
            return True
            
        except Exception as e:
            print(f"❌ 加载数据失败: {e}")
            return False
    
    def calculate_metrics(self):
        """计算评估指标"""
        if 'actual' not in self.df.columns or 'predicted' not in self.df.columns:
            print("⚠️ 缺少actual或predicted列，无法计算指标")
            return None
        
        actual = self.df['actual'].values
        predicted = self.df['predicted'].values
        
        # 计算指标
        mae = np.mean(np.abs(actual - predicted))
        rmse = np.sqrt(np.mean((actual - predicted) ** 2))
        mape = np.mean(np.abs((actual - predicted) / (actual + 1e-8))) * 100
        
        metrics = {
            'MAE': mae,
            'RMSE': rmse,
            'MAPE': mape,
            'R²': np.corrcoef(actual, predicted)[0, 1] ** 2 if len(actual) > 1 else 0
        }
        
        return metrics
    
    def create_comparison_plot(self, save_path=None):
        """创建预测vs实际对比图"""
        if self.df is None:
            print("❌ 请先加载数据")
            return
        
        # 创建图形
        fig, axes = plt.subplots(2, 2, figsize=(15, 10))
        fig.suptitle(f'预测结果分析 - {os.path.basename(self.csv_file)}', fontsize=16, fontweight='bold')
        
        # 1. 时间序列对比图
        ax1 = axes[0, 0]
        if 'actual' in self.df.columns and 'predicted' in self.df.columns:
            ax1.plot(self.df['dateTime'], self.df['actual'], label='实际值', color='blue', alpha=0.7)
            ax1.plot(self.df['dateTime'], self.df['predicted'], label='预测值', color='red', alpha=0.7)
            ax1.set_title('预测 vs 实际值对比')
            ax1.set_xlabel('时间')
            ax1.set_ylabel('负荷值')
            ax1.legend()
            ax1.grid(True, alpha=0.3)
        else:
            # 如果只有预测值
            pred_col = 'predicted' if 'predicted' in self.df.columns else self.df.columns[-1]
            ax1.plot(self.df['dateTime'], self.df[pred_col], label='预测值', color='red', alpha=0.7)
            ax1.set_title('预测值时间序列')
            ax1.set_xlabel('时间')
            ax1.set_ylabel('负荷值')
            ax1.legend()
            ax1.grid(True, alpha=0.3)
        
        # 2. 散点图 (如果有实际值)
        ax2 = axes[0, 1]
        if 'actual' in self.df.columns and 'predicted' in self.df.columns:
            ax2.scatter(self.df['actual'], self.df['predicted'], alpha=0.6, color='green')
            
            # 添加完美预测线
            min_val = min(self.df['actual'].min(), self.df['predicted'].min())
            max_val = max(self.df['actual'].max(), self.df['predicted'].max())
            ax2.plot([min_val, max_val], [min_val, max_val], 'r--', label='完美预测线')
            
            ax2.set_xlabel('实际值')
            ax2.set_ylabel('预测值')
            ax2.set_title('预测准确性散点图')
            ax2.legend()
            ax2.grid(True, alpha=0.3)
        else:
            # 预测值分布直方图
            pred_col = 'predicted' if 'predicted' in self.df.columns else self.df.columns[-1]
            ax2.hist(self.df[pred_col], bins=30, alpha=0.7, color='skyblue', edgecolor='black')
            ax2.set_title('预测值分布')
            ax2.set_xlabel('负荷值')
            ax2.set_ylabel('频次')
            ax2.grid(True, alpha=0.3)
        
        # 3. 误差分析 (如果有实际值)
        ax3 = axes[1, 0]
        if 'actual' in self.df.columns and 'predicted' in self.df.columns:
            errors = self.df['predicted'] - self.df['actual']
            ax3.plot(self.df['dateTime'], errors, color='purple', alpha=0.7)
            ax3.axhline(y=0, color='black', linestyle='--', alpha=0.5)
            ax3.set_title('预测误差时间序列')
            ax3.set_xlabel('时间')
            ax3.set_ylabel('误差 (预测值 - 实际值)')
            ax3.grid(True, alpha=0.3)
        else:
            # 预测值变化率
            pred_col = 'predicted' if 'predicted' in self.df.columns else self.df.columns[-1]
            changes = self.df[pred_col].diff()
            ax3.plot(self.df['dateTime'][1:], changes[1:], color='orange', alpha=0.7)
            ax3.set_title('预测值变化率')
            ax3.set_xlabel('时间')
            ax3.set_ylabel('变化量')
            ax3.grid(True, alpha=0.3)
        
        # 4. 指标展示
        ax4 = axes[1, 1]
        ax4.axis('off')
        
        # 计算并显示指标
        metrics = self.calculate_metrics()
        if metrics:
            metrics_text = f"""
评估指标:

MAE (平均绝对误差): {metrics['MAE']:.4f}
RMSE (均方根误差): {metrics['RMSE']:.4f}
MAPE (平均绝对百分比误差): {metrics['MAPE']:.2f}%
R² (决定系数): {metrics['R²']:.4f}

数据信息:
样本数量: {len(self.df)}
时间范围: {self.df['dateTime'].min().strftime('%Y-%m-%d %H:%M')} 
         至 {self.df['dateTime'].max().strftime('%Y-%m-%d %H:%M')}
"""
        else:
            metrics_text = f"""
数据信息:

文件: {os.path.basename(self.csv_file)}
样本数量: {len(self.df)}
列数: {len(self.df.columns)}
时间范围: {self.df['dateTime'].min().strftime('%Y-%m-%d %H:%M')} 
         至 {self.df['dateTime'].max().strftime('%Y-%m-%d %H:%M')}

注: 仅包含预测值，无法计算准确性指标
"""
        
        ax4.text(0.1, 0.9, metrics_text, transform=ax4.transAxes, fontsize=11,
                verticalalignment='top', bbox=dict(boxstyle='round', facecolor='lightblue', alpha=0.8))
        
        plt.tight_layout()
        
        # 保存图片
        if save_path is None:
            save_path = self.csv_file.replace('.csv', '_visualization.png')
        
        plt.savefig(save_path, dpi=300, bbox_inches='tight')
        print(f"✅ 可视化图片已保存: {save_path}")
        
        plt.show()
        
        return fig

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='预测结果可视化工具')
    parser.add_argument('--file', '-f', type=str, help='指定CSV文件路径')
    parser.add_argument('--output', '-o', type=str, help='指定输出图片路径')
    
    args = parser.parse_args()
    
    print("🎨 预测结果可视化工具")
    print("=" * 50)
    
    # 创建可视化器
    visualizer = PredictionVisualizer(args.file)
    
    # 加载数据
    if visualizer.load_data():
        # 创建可视化
        visualizer.create_comparison_plot(args.output)
    else:
        print("❌ 可视化失败")

if __name__ == "__main__":
    main()
