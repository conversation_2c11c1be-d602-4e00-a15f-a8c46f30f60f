from abc import ABC, abstractmethod

class BasedModel(ABC):
    def __init__(self):
        self.model = None
        
    @abstractmethod
    def train(self, X_train, y_train):
        # 训练模型
        pass
    
    @abstractmethod
    def predict(self, X_test):
        # 预测
        pass
    
    @abstractmethod
    def save(self, path):
        # 保存模型
        pass
    
    @abstractmethod
    def load(self, path):
        # 加载模型
        pass