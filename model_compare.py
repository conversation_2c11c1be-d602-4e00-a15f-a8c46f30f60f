# from model_evaluator import ModelEvaluator

# class ModelManager:
#     def __init__(self, data_preprocessor, X_train=None, y_train=None, X_test=None, y_test=None):
#         self.data_preprocessor = data_preprocessor
#         self.models = {}
#         self.evaluation_results = {}
#         self.X_train = X_train
#         self.y_train = y_train
#         self.X_test = X_test
#         self.y_test = y_test
        
#     def add_model(self, model_name, model):
#         # 添加模型到管理器
#         self.models[model_name] = model
        
#     def train_all_models(self):
#         # 训练所有模型
#         if self.X_train is not None and self.y_train is not None:
#             X_train, y_train = self.X_train, self.y_train
#         else:
#             X_train, y_train = self.data_preprocessor.get_train_data()
            
#         for name, model in self.models.items():
#             print(f"Training {name}...")
#             model.train(X_train, y_train)
            
#     def evaluate_all_models(self):
#         # 评估所有模型
#         if self.X_test is not None and self.y_test is not None:
#             X_test, y_test = self.X_test, self.y_test
#         else:
#             X_test, y_test = self.data_preprocessor.get_test_data()
            
#         evaluator = ModelEvaluator()
        
#         for name, model in self.models.items():
#             print(f"Evaluating {name}...")
#             y_pred = model.predict(X_test)
#             metrics = evaluator.calculate_all_metrics(y_test, y_pred)
#             self.evaluation_results[name] = metrics
            
#     def get_best_model(self, metric='accuracy'):
#         # 获取表现最好的模型：若只有一个模型，直接返回；否则按指标比较
#         if not self.evaluation_results:
#             return None, None
        
#         # 只有一个模型时，直接返回该模型
#         if len(self.evaluation_results) == 1:
#             model_name = next(iter(self.evaluation_results.keys()))
#             return model_name, self.evaluation_results[model_name]
        
#         # 多个模型时，按指标比较（数值越大越好的指标）
#         best_model_name = max(
#             self.evaluation_results.keys(), 
#             key=lambda name: self.evaluation_results[name][metric]
#         )
#         return best_model_name, self.evaluation_results[best_model_name]
from model_evaluator import ModelEvaluator
import pandas as pd

class ModelManager:
    def __init__(self, data_preprocessor, X_train=None, y_train=None, X_test=None, y_test=None, 
                 dates_train=None, dates_test=None):
        """
        初始化模型管理器
        
        参数:
            data_preprocessor: 数据预处理器实例
            X_train, y_train: 训练数据特征和标签
            X_test, y_test: 测试数据特征和标签
            dates_train, dates_test: 训练集和测试集对应的日期
        """
        self.data_preprocessor = data_preprocessor
        self.models = {}
        self.evaluation_results = {}
        self.X_train = X_train
        self.y_train = y_train
        self.X_test = X_test
        self.y_test = y_test
        self.dates_train = dates_train  # 新增：训练集日期
        self.dates_test = dates_test    # 新增：测试集日期
        
    def add_model(self, model_name, model):
        # 添加模型到管理器
        self.models[model_name] = model
        
    def train_all_models(self):
        # 训练所有模型
        if self.X_train is not None and self.y_train is not None:
            X_train, y_train = self.X_train, self.y_train
        else:
            X_train, y_train = self.data_preprocessor.get_train_data()
            
        for name, model in self.models.items():
            print(f"Training {name}...")
            model.train(X_train, y_train)
            
    def evaluate_all_models(self):
        # 评估所有模型
        if self.X_test is not None and self.y_test is not None:
            X_test, y_test = self.X_test, self.y_test
        else:
            X_test, y_test = self.data_preprocessor.get_test_data()
            
        evaluator = ModelEvaluator()
        
        for name, model in self.models.items():
            print(f"Evaluating {name}...")
            y_pred = model.predict(X_test)

            # 确保预测结果是一维数组或Series
            if isinstance(y_pred, pd.DataFrame):
                y_pred = y_pred['value'].values

            # 处理LSTM等序列模型预测结果数量不匹配的问题
            if len(y_pred) != len(y_test):
                print(f"  ⚠️ {name}预测结果数量({len(y_pred)})与测试集({len(y_test)})不匹配")
                if len(y_pred) < len(y_test):
                    # 如果预测结果较少，截取对应的测试集部分
                    y_test_aligned = y_test.iloc[-len(y_pred):] if hasattr(y_test, 'iloc') else y_test[-len(y_pred):]
                    print(f"  📊 使用最后{len(y_pred)}个样本进行评估")
                else:
                    # 如果预测结果较多，截取对应的预测结果部分
                    y_pred = y_pred[:len(y_test)]
                    y_test_aligned = y_test
                    print(f"  📊 截取前{len(y_test)}个预测结果进行评估")
            else:
                y_test_aligned = y_test

            # 计算基础指标
            metrics = evaluator.calculate_all_metrics(y_test_aligned, y_pred)

            # 计算业务准确率
            accuracy_trade = evaluator.accuracy_for_trade(y_test_aligned, y_pred)
            metrics['accuracy_for_trade'] = accuracy_trade

            self.evaluation_results[name] = metrics

            # 打印详细评估结果
            print(f"  {name}评估结果:")
            for metric, value in metrics.items():
                if metric == 'accuracy_for_trade':
                    print(f"    {metric}: {value:.4f} ({value*100:.2f}%)")
                else:
                    print(f"    {metric}: {value:.4f}")
        
    def get_best_model(self, metric='rmse'):
        """
        获取表现最好的模型
        
        参数:
            metric: 评估指标名称，默认为'rmse'（越小越好）
        """
        if not self.evaluation_results:
            return None, None
        
        # 确定指标是越小越好还是越大越好
        is_lower_better = metric in ['rmse', 'mae', 'mse', 'mape', 'smape']
        is_higher_better = metric in ['r2', 'accuracy_for_trade']
        
        # 只有一个模型时，直接返回该模型
        if len(self.evaluation_results) == 1:
            model_name = next(iter(self.evaluation_results.keys()))
            return model_name, self.evaluation_results[model_name]
        
        # 多个模型时，按指标比较
        if is_lower_better:
            best_model_name = min(
                self.evaluation_results.keys(),
                key=lambda name: self.evaluation_results[name][metric]
            )
        elif is_higher_better:
            best_model_name = max(
                self.evaluation_results.keys(),
                key=lambda name: self.evaluation_results[name][metric]
            )
        else:
            # 默认情况，假设越大越好
            best_model_name = max(
                self.evaluation_results.keys(),
                key=lambda name: self.evaluation_results[name][metric]
            )
            
        return best_model_name, self.evaluation_results[best_model_name]
    
    def predict_future(self, model_name, history_data, weather_data, future_days=7):
        """
        使用指定模型预测未来数据
        
        参数:
            model_name: 模型名称
            history_data: 历史数据
            weather_data: 天气数据（包含未来日期的预报）
            future_days: 预测未来的天数
            
        返回:
            预测结果DataFrame
        """
        if model_name not in self.models:
            raise ValueError(f"模型 '{model_name}' 不存在")
            
        model = self.models[model_name]
        
        print(f"使用 {model_name} 模型预测未来 {future_days} 天数据...")
        predictions = model.predict_future(
            history_data=history_data,
            weather_data=weather_data,
            future_days=future_days
        )
        
        return predictions
