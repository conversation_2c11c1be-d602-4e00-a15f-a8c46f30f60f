# import numpy as np
# from sklearn.metrics import mean_squared_error, mean_absolute_error

# class ModelEvaluator:
#     def __init__(self):
#         pass
    
#     def calculate_rmse(self, y_true, y_pred):
#         """计算均方根误差 (Root Mean Squared Error)"""
#         return np.sqrt(mean_squared_error(y_true, y_pred))
    
#     def calculate_mae(self, y_true, y_pred):
#         """计算平均绝对误差 (Mean Absolute Error)"""
#         return mean_absolute_error(y_true, y_pred)
    
#     def calculate_mape(self, y_true, y_pred):
#         """计算平均绝对百分比误差 (Mean Absolute Percentage Error)"""
#         # 避免除零错误
#         mask = y_true != 0
#         return np.mean(np.abs((y_true[mask] - y_pred[mask]) / y_true[mask])) * 100
    
#     def calculate_r2(self, y_true, y_pred):
#         """计算R²分数 (Coefficient of Determination)"""
#         ss_res = np.sum((y_true - y_pred) ** 2)
#         ss_tot = np.sum((y_true - np.mean(y_true)) ** 2)
#         return 1 - (ss_res / ss_tot) if ss_tot != 0 else 0
    
#     def calculate_all_metrics(self, y_true, y_pred):
#         """计算所有评估指标并处理无效值（inf/NaN）"""
#         # 基础计算
#         rmse = self.calculate_rmse(y_true, y_pred)
#         mae = self.calculate_mae(y_true, y_pred)
#         mape = self.calculate_mape(y_true, y_pred)
#         r2 = self.calculate_r2(y_true, y_pred)
        
#         # 处理无效值（根据指标特性设置极端值）
#         # 1. rmse/mae/mape：越小越好 → 无效值替换为极大值（视为“最差结果”）
#         if not np.isfinite(rmse):
#             rmse = 1e10  # 替换为一个足够大的数
#         if not np.isfinite(mae):
#             mae = 1e10
#         if not np.isfinite(mape):
#             mape = 1e10  # 例如：原计算出现 inf（除零），替换为大值
        
#         # 2. r2：越大越好 → 无效值替换为极小值（视为“最差结果”）
#         if not np.isfinite(r2):
#             r2 = -1e10  # 替换为一个足够小的数（r2 最小值理论上为 -inf）
        
#         return {
#             'rmse': rmse,
#             'mae': mae,
#             'mape': mape,
#             'r2': r2
#         }
    
# if __name__ == "__main__":
#     a = ModelEvaluator() 

import numpy as np
import pandas as pd
from sklearn.metrics import mean_squared_error, mean_absolute_error

class ModelEvaluator:
    def __init__(self):
        pass
    
    def calculate_rmse(self, y_true, y_pred):
        """计算均方根误差 (Root Mean Squared Error)"""
        return np.sqrt(mean_squared_error(y_true, y_pred))
    
    def calculate_mae(self, y_true, y_pred):
        """计算平均绝对误差 (Mean Absolute Error)"""
        return mean_absolute_error(y_true, y_pred)
    
    def calculate_mape(self, y_true, y_pred):
        """计算平均绝对百分比误差 (Mean Absolute Percentage Error)"""
        # 避免除零错误
        valid_mask = (y_true != 0) & np.isfinite(y_true) & np.isfinite(y_pred)
        if not np.any(valid_mask):
            return np.nan  # 无有效数据点
        return np.mean(np.abs((y_true[valid_mask] - y_pred[valid_mask]) / y_true[valid_mask])) * 100
    
    def calculate_smape(self, y_true, y_pred):
        """计算对称平均绝对百分比误差 (Symmetric Mean Absolute Percentage Error)"""
        valid_mask = (np.abs(y_true) + np.abs(y_pred) > 0) & np.isfinite(y_true) & np.isfinite(y_pred)
        if not np.any(valid_mask):
            return np.nan
        return np.mean(2 * np.abs(y_true[valid_mask] - y_pred[valid_mask]) / 
                      (np.abs(y_true[valid_mask]) + np.abs(y_pred[valid_mask]))) * 100
    
    def calculate_r2(self, y_true, y_pred):
        """计算R²分数 (Coefficient of Determination)"""
        valid_mask = np.isfinite(y_true) & np.isfinite(y_pred)
        if not np.any(valid_mask):
            return np.nan
        
        y_true_valid = y_true[valid_mask]
        y_pred_valid = y_pred[valid_mask]
        
        ss_res = np.sum((y_true_valid - y_pred_valid) ** 2)
        ss_tot = np.sum((y_true_valid - np.mean(y_true_valid)) ** 2)
        
        return 1 - (ss_res / ss_tot) if ss_tot != 0 else 0
    
    def accuracy_for_trade(self, y_true, y_pred, epsilon = 0.01):
        # 定义业务意义上的“接近零”的阈值epsilon,当预测值、真实值都≤epsilon时，准确率为1，避免分母为0
        y_true = np.asarray(y_true)
        y_pred = np.asarray(y_pred)

        # 初始化准确率数组
        accuracies = np.zeros_like(y_true, dtype=float)

        # 判断哪些样本是“真实值和预测值都小于等于 0.01” → 视为准确预测
        near_zero_mask = (np.abs(y_true) <= epsilon) & (np.abs(y_pred) <= epsilon)
        accuracies[near_zero_mask] = 1.0

        # 处理剩下的样本（非 near-zero），计算 MAPE
        non_zero_mask = ~near_zero_mask
        if np.any(non_zero_mask):
            y_true_nonzero = y_true[non_zero_mask]
            y_pred_nonzero = y_pred[non_zero_mask]

            # 计算 MAPE：这里只加一个极小量防止除零，不干扰业务逻辑
            mape_values = np.abs((y_pred_nonzero - y_true_nonzero) / np.clip(y_true_nonzero, a_min=1e-8, a_max=None))
            accuracies[non_zero_mask] = 1 - mape_values

            # 限制最小准确率为 0（MAPE > 1 的情况）
            accuracies[non_zero_mask] = np.clip(accuracies[non_zero_mask], 0, 1)

        # 返回平均准确率
        return np.mean(accuracies)
    
    def calculate_all_metrics(self, y_true, y_pred):
        """计算所有评估指标并处理无效值（inf/NaN）"""
        # 确保输入是numpy数组
        if isinstance(y_true, pd.Series):
            y_true = y_true.values
        if isinstance(y_pred, pd.Series):
            y_pred = y_pred.values
        if isinstance(y_pred, pd.DataFrame):
            y_pred = y_pred['value'].values
            
        # 基础计算
        metrics = {
            'rmse': self.calculate_rmse(y_true, y_pred),
            'mae': self.calculate_mae(y_true, y_pred),
            'mape': self.calculate_mape(y_true, y_pred),
            'smape': self.calculate_smape(y_true, y_pred),  # 新增SMAPE
            'r2': self.calculate_r2(y_true, y_pred)
        }
        
        # 处理无效值（根据指标特性设置极端值）
        for metric_name, value in metrics.items():
            if not np.isfinite(value):
                if metric_name in ['rmse', 'mae', 'mape', 'smape']:
                    metrics[metric_name] = 1e10  # 误差类指标：替换为极大值
                elif metric_name == 'r2':
                    metrics[metric_name] = -1e10  # R²：替换为极小值
        
        return metrics
    
    def calculate_metrics_by_time(self, y_true, y_pred, dates, time_period='day'):
        """
        按时间周期（日/周/月）计算评估指标
        
        参数:
            y_true: 真实值
            y_pred: 预测值
            dates: 对应的日期时间序列
            time_period: 时间周期，可选 'day', 'week', 'month'
        """
        if not isinstance(dates, pd.DatetimeIndex):
            dates = pd.to_datetime(dates)
            
        # 创建时间分组键
        if time_period == 'day':
            time_key = dates.date
        elif time_period == 'week':
            time_key = dates.isocalendar().week
        elif time_period == 'month':
            time_key = dates.to_period('M')
        else:
            raise ValueError(f"不支持的时间周期: {time_period}")
            
        # 按时间分组计算指标
        time_metrics = {}
        
        for period, group_idx in pd.Series(y_true).groupby(time_key).groups.items():
            if isinstance(group_idx, int):
                group_idx = [group_idx]  # 处理只有一个元素的情况
                
            y_true_period = y_true[group_idx]
            y_pred_period = y_pred[group_idx]
            
            # 跳过全为无效值的组
            if not np.any(np.isfinite(y_true_period)) or not np.any(np.isfinite(y_pred_period)):
                continue
                
            period_metrics = self.calculate_all_metrics(y_true_period, y_pred_period)
            time_metrics[period] = period_metrics
            
        return time_metrics
