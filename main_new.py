import sys
import os
# 添加 algorithms 目录到 Python 路径
sys.path.append(os.path.join(os.path.dirname(__file__), 'algorithms'))

import pandas as pd
from datetime import datetime, timedelta
from load_tabpfn_customer import LoadTabpfn
# 导入数据预处理类（确保DataPreprocessor所在文件正确导入）
from data_loader import DataPreprocessor  # 假设DataPreprocessor在data_loader.py中


if __name__ == "__main__":
    # -----------准备参数----------- #
    company_name = "双能低碳"
    customer_code = "山西阳光焦化集团股份有限公司"  # 客户代码

    # 滚动预测参数
    start_date = "2025-05-01"  # 开始日期
    end_date = "2025-07-17"    # 结束日期（预测到这一天）
    history_days = 35          # 使用的历史天数

    # -----------初始化数据预处理类----------- #
    # 创建DataPreprocessor实例，统一管理数据加载
    preprocessor = DataPreprocessor(
        start_date=start_date,
        end_date=end_date,
        excel_file_path='山西零售用户负荷.xlsx',  # 负荷数据路径
        weather_csv_path="weather_山西.csv"       # 天气数据路径
    )

    # -----------使用类方法加载数据----------- #
    # 1. 加载负荷数据（自动识别频率）
    df_real_load = preprocessor.load_user_load(customer_code = customer_code)
    print(f"加载了真实负荷数据，共 {len(df_real_load)} 条记录")
    print(f"负荷数据时间范围: {df_real_load['dateTime'].min()} 到 {df_real_load['dateTime'].max()}")

    # 2. 加载天气数据（自动使用负荷数据的频率）
    df_weather = preprocessor.load_weather_data()
    print(f"加载了本地天气数据，共 {len(df_weather)} 条记录")
    print(f"天气数据时间范围: {df_weather['t_datetime_cst'].min()} 到 {df_weather['t_datetime_cst'].max()}")

    # 3. 对天气数据做特征工程
    df_weather_with_features = preprocessor.process_weather_features(df_weather)

    # 4. 合并负荷和天气数据（如果需要直接使用合并后的数据）
    merged_data = pd.merge(
        df_real_load,
        df_weather_with_features,  # 使用带特征的天气数据
        on="dateTime",
        how="inner"
    )
    # -------------- 创建模型实例 -------------- #
    print(f"\n=== 开始滚动预测 ===")
    print(f"公司名称: {company_name}")
    print(f"客户代码: {customer_code}")
    print(f"预测范围: {start_date} 到 {end_date}")
    print(f"历史天数: {history_days} 天")
    print(f"数据频率: {preprocessor.load_freq}（自动同步）")  # 显示自动识别的频率



    df_merged = pd.merge(df_weather, df_real_load, on='dateTime')
    model = LoadTabpfn(
        customer_code=customer_code,
        df_data=df_merged  # 传入合并后的数据，而不是单独的天气数据
    )

    # 4. 执行滚动预测（核心逻辑不变，使用类加载的数据）
    print(f"\n=== 开始执行滚动预测 ===")
    import time

    total_start_time = time.time()

    # 生成日期列表
    start_dt = datetime.strptime(start_date, "%Y-%m-%d")
    end_dt = datetime.strptime(end_date, "%Y-%m-%d")

    # 创建完整的时间序列（使用自动识别的频率）
    full_time_range = pd.date_range(
        start = start_dt,
        end = end_dt + timedelta(days=1) - timedelta(seconds=1),
        freq = preprocessor.load_freq  # 替换为自动识别的频率
    )

    # 初始化结果DataFrame
    result_df = pd.DataFrame({
        'dateTime': full_time_range,
        'value': 0.0,
        'type': 'unknown'  # 'real' 表示真实值，'predicted' 表示预测值
    })

    # 设置前35天为真实值
    first_pred_date = start_dt + timedelta(days=history_days)
    print(f"前 {history_days} 天使用真实值，从 {first_pred_date.strftime('%Y-%m-%d')} 开始预测")

    # 填入前35天的真实负荷数据
    for i, row in df_real_load.iterrows():
        mask = result_df['dateTime'] == row['dateTime']
        if mask.any():
            result_df.loc[mask, 'value'] = row['load']
            result_df.loc[mask, 'type'] = 'real'

    print(f"已填入前 {history_days} 天的真实负荷数据")

    # 开始滚动预测
    current_date = first_pred_date
    prediction_count = 0

    while current_date <= end_dt:
        prediction_date_str = current_date.strftime("%Y-%m-%d")
        prediction_count += 1

        print(f"\n📅 第 {prediction_count} 次预测: {prediction_date_str}")

        try:
            # 执行单日预测
            daily_result = model.pred(prediction_date_str)

            # 将预测结果填入结果DataFrame
            for timestamp, value in daily_result.iterrows():
                mask = result_df['dateTime'] == timestamp
                if mask.any():
                    result_df.loc[mask, 'value'] = value['value']
                    result_df.loc[mask, 'type'] = 'predicted'

            print(f"   ✅ 预测完成，共 {len(daily_result)} 个时间点")

        except Exception as e:
            print(f"   ❌ 预测失败: {str(e)}")
            # 如果预测失败，使用前一天同时刻的值作为备用
            prev_date = current_date - timedelta(days=1)
            prev_mask = (result_df['dateTime'].dt.date == prev_date.date())
            curr_mask = (result_df['dateTime'].dt.date == current_date.date())

            if prev_mask.any() and curr_mask.any():
                prev_values = result_df.loc[prev_mask, 'value'].values
                result_df.loc[curr_mask, 'value'] = prev_values
                result_df.loc[curr_mask, 'type'] = 'fallback'
                print(f"   🔄 使用前一天数据作为备用")

        # 移动到下一天
        current_date += timedelta(days=1)

    total_time = time.time() - total_start_time
    print(f"\n=== 滚动预测完成 ===")
    print(f"总耗时: {total_time:.2f} 秒")
    print(f"总预测天数: {prediction_count} 天")

    # 5. 统计结果
    real_count = (result_df['type'] == 'real').sum()
    pred_count = (result_df['type'] == 'predicted').sum()
    fallback_count = (result_df['type'] == 'fallback').sum()

    print(f"\n📊 结果统计:")
    print(f"真实值数据点: {real_count}")
    print(f"预测值数据点: {pred_count}")
    print(f"备用值数据点: {fallback_count}")
    print(f"总数据点: {len(result_df)}")

    # 6. 保存结果
    output_filename = f"{company_name}_滚动预测结果_{start_date}_to_{end_date}.csv"
    result_df.to_csv(output_filename, index=False)
    print(f"\n💾 结果已保存到: {output_filename}")

    # 7. 显示部分结果
    print(f"\n📋 前10行结果:")
    print(result_df.head(10))
    print(f"\n📋 后10行结果:")
    print(result_df.tail(10))