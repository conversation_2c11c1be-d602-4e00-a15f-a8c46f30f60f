# from my_basic_model import MyBasicModel  
# from tabpfn import TabPFNRegressor
# import torch
# import pandas as pd
# import numpy as np
# import time
# import joblib  # 用于模型保存/加载

# # 自动处理缺失值、归一化，支持GPU加速
# class TabPfn(MyBasicModel):
#     def __init__(self, device='auto', random_state=80):
#         """
#         初始化TabPFN模型，支持GPU加速
        
#         参数:
#             device: 计算设备 ('auto', 'cpu', 'cuda')。'auto'会自动检测GPU
#             random_state: 随机种子，确保结果可复现
#         """
#         super().__init__()  # 调用父类初始化
        
#         # 设备配置（核心：GPU加速支持）
#         self.device = self._resolve_device(device)  # 解析设备参数
#         self.random_state = random_state
#         self.scaler = None
#         self.feature_cols = None

#         # 初始化模型（已支持GPU）
#         self.model = self._default_model()


#     def _resolve_device(self, device: str) -> str:
#         """解析设备参数，确保GPU可用时优先使用"""
#         if device == 'auto':
#             # 自动检测：有GPU则用cuda，否则用cpu
#             return 'cuda' if torch.cuda.is_available() else 'cpu'
#         elif device in ['cuda', 'cpu']:
#             # 手动指定时，检查cuda是否可用
#             if device == 'cuda' and not torch.cuda.is_available():
#                 print("⚠️ 指定了'cuda'但未检测到GPU，自动切换到'cpu'")
#                 return 'cpu'
#             return device
#         else:
#             # 无效参数时默认用cpu
#             print(f"⚠️ 无效设备参数'{device}'，使用'cpu'")
#             return 'cpu'


#     def _default_model(self) -> TabPFNRegressor:
#         """初始化TabPFN回归模型，强制使用解析后的设备"""
#         return TabPFNRegressor(
#             random_state=self.random_state,  # 固定随机种子
#             device=self.device,  # 使用解析后的设备（确保是cuda或cpu）
#             ignore_pretraining_limits=True  # 允许超过预训练特征限制
#         )


#     def train(self, X_train: pd.DataFrame, y_train: pd.Series):
#         """训练模型（自动使用指定设备加速）"""
#         print(f"开始训练TabPFN模型（设备：{self.device}），使用 {len(X_train)} 条样本")
#         self.feature_cols = X_train.columns.tolist()
        
#         train_start = time.time()
#         # 训练时模型会自动使用指定的设备（GPU/CPU）
#         self.model.fit(X_train, y_train)
#         print(f"训练完成，耗时: {time.time() - train_start:.2f} 秒")


#     def predict(self, X_test: pd.DataFrame) -> pd.DataFrame:
#         """预测（使用训练时的设备加速）"""
#         if self.feature_cols is None:
#             raise ValueError("模型尚未训练，请先调用train()方法")
            
#         # 确保特征顺序一致
#         X_test = X_test[self.feature_cols]
        
#         # 处理缺失值
#         if X_test.isnull().any().any():
#             print("⚠️ 测试集存在缺失值，使用前向填充处理")
#             X_test = X_test.ffill().bfill()
            
#         print(f"开始预测（设备：{self.device}），样本数量: {len(X_test)}")
#         pred_start = time.time()
        
#         # 预测时自动使用训练时的设备
#         y_pred = self.model.predict(X_test)
#         y_pred = np.clip(y_pred, 0, None)  # 负荷预测确保非负
        
#         print(f"预测完成，耗时: {time.time() - pred_start:.2f} 秒")
#         return pd.DataFrame({'value': y_pred})


#     def save(self, path: str):
#         """保存模型（包含设备信息，加载时自动匹配）"""
#         model_state = {
#             'model': self.model,
#             'feature_cols': self.feature_cols,
#             'scaler': self.scaler,
#             'device': self.device,  # 保存训练时的设备
#             'random_state': self.random_state
#         }
#         joblib.dump(model_state, path)
#         print(f"模型已保存至: {path}（训练设备：{self.device}）")


#     def load(self, path: str):
#         """加载模型（自动适配当前设备）"""
#         model_state = joblib.load(path)
#         self.model = model_state['model']
#         self.feature_cols = model_state['feature_cols']
#         self.scaler = model_state['scaler']
#         self.random_state = model_state['random_state']
        
#         # 加载时检查设备兼容性
#         saved_device = model_state['device']
#         current_device = self._resolve_device(saved_device)  # 重新解析设备
#         self.device = current_device
        
#         # 如果保存的是GPU模型但当前只有CPU，自动切换
#         if saved_device == 'cuda' and current_device == 'cpu':
#             print("⚠️ 加载的是GPU训练的模型，当前无GPU，将在CPU上运行（可能较慢）")
        
#         print(f"模型已从 {path} 加载（当前运行设备：{self.device}）")


# from my_basic_model import MyBasicModel  
# from tabpfn import TabPFNRegressor
# import torch
# import pandas as pd
# import numpy as np
# import time
# import joblib  # 用于模型保存/加载
# from datetime import datetime, timedelta

# # 自动处理缺失值、归一化，支持GPU加速
# class TabPfn(MyBasicModel):
#     def __init__(self, device='auto', random_state=80, history_days=35):
#         """
#         初始化TabPFN模型，支持GPU加速
        
#         参数:
#             device: 计算设备 ('auto', 'cpu', 'cuda')。'auto'会自动检测GPU
#             random_state: 随机种子，确保结果可复现
#             history_days: 历史窗口大小（用于预测）
#         """
#         super().__init__()  # 调用父类初始化
        
#         # 设备配置（核心：GPU加速支持）
#         self.device = self._resolve_device(device)  # 解析设备参数
#         self.random_state = random_state
#         self.history_days = history_days  # 新增：历史窗口大小
#         self.scaler = None
#         self.feature_cols = None
#         self.points_per_day = None  # 每天的数据点数（根据频率计算）
#         self.freq = None  # 数据频率
        
#         # 初始化模型（已支持GPU）
#         self.model = self._default_model()


#     def _resolve_device(self, device: str) -> str:
#         """解析设备参数，确保GPU可用时优先使用"""
#         if device == 'auto':
#             # 自动检测：有GPU则用cuda，否则用cpu
#             return 'cuda' if torch.cuda.is_available() else 'cpu'
#         elif device in ['cuda', 'cpu']:
#             # 手动指定时，检查cuda是否可用
#             if device == 'cuda' and not torch.cuda.is_available():
#                 print("⚠️ 指定了'cuda'但未检测到GPU，自动切换到'cpu'")
#                 return 'cpu'
#             return device
#         else:
#             # 无效参数时默认用cpu
#             print(f"⚠️ 无效设备参数'{device}'，使用'cpu'")
#             return 'cpu'


#     def _default_model(self) -> TabPFNRegressor:
#         """初始化TabPFN回归模型，强制使用解析后的设备"""
#         return TabPFNRegressor(
#             random_state=self.random_state,  # 固定随机种子
#             device=self.device,  # 使用解析后的设备（确保是cuda或cpu）
#             ignore_pretraining_limits=True  # 允许超过预训练特征限制
#         )


#     def train(self, X_train: pd.DataFrame, y_train: pd.Series):
#         """训练模型（自动使用指定设备加速）"""
#         print(f"开始训练TabPFN模型（设备：{self.device}），使用 {len(X_train)} 条样本")
#         self.feature_cols = X_train.columns.tolist()
        
#         train_start = time.time()
#         # 训练时模型会自动使用指定的设备（GPU/CPU）
#         self.model.fit(X_train, y_train)
#         print(f"训练完成，耗时: {time.time() - train_start:.2f} 秒")


#     def predict(self, X_test: pd.DataFrame) -> pd.DataFrame:
#         """预测（使用训练时的设备加速）"""
#         if self.feature_cols is None:
#             raise ValueError("模型尚未训练，请先调用train()方法")
            
#         # 确保特征顺序一致
#         X_test = X_test[self.feature_cols]
        
#         # 处理缺失值
#         if X_test.isnull().any().any():
#             print("⚠️ 测试集存在缺失值，使用前向填充处理")
#             X_test = X_test.ffill().bfill()
            
#         print(f"开始预测（设备：{self.device}），样本数量: {len(X_test)}")
#         pred_start = time.time()
        
#         # 预测时自动使用训练时的设备
#         y_pred = self.model.predict(X_test)
#         y_pred = np.clip(y_pred, 0, None)  # 负荷预测确保非负
        
#         print(f"预测完成，耗时: {time.time() - pred_start:.2f} 秒")
#         return pd.DataFrame({'value': y_pred})


#     def predict_future(self, history_data: pd.DataFrame, weather_data: pd.DataFrame, future_days: int = 7) -> pd.DataFrame:
#         """
#         使用历史数据和天气预报预测未来负荷
        
#         参数:
#             history_data: 历史数据（包含负荷和天气特征）
#             weather_data: 天气数据（包含未来日期的天气预报）
#             future_days: 预测未来的天数
            
#         返回:
#             预测结果DataFrame，包含日期和预测负荷值
#         """
#         if self.feature_cols is None:
#             raise ValueError("模型尚未训练，请先调用train()方法")
            
#         # 确保数据按时间排序
#         if not history_data['dateTime'].is_monotonic_increasing:
#             history_data = history_data.sort_values('dateTime')
            
#         # 获取数据频率（如果尚未设置）
#         if self.freq is None:
#             # 计算前两个时间点的差值，作为频率
#             if len(history_data) >= 2:
#                 time_delta = history_data['dateTime'].iloc[1] - history_data['dateTime'].iloc[0]
#                 self.freq = self._timedelta_to_freq(time_delta)
#                 print(f"识别到数据频率：{self.freq}")
                
#                 # 计算每天的数据点数
#                 if self.freq.endswith('H'):  # 小时频率
#                     self.points_per_day = 24 // int(self.freq[:-1])
#                 elif self.freq.endswith('T'):  # 分钟频率
#                     self.points_per_day = 1440 // int(self.freq[:-1])
#                 else:
#                     self.points_per_day = 1  # 默认每天1个点
#                 print(f"每天数据点数：{self.points_per_day}")
#             else:
#                 raise ValueError("历史数据点数不足（至少需要2条），无法识别频率")
                
#         # 确保有足够的历史数据
#         history_points_needed = self.history_days * self.points_per_day
#         if len(history_data) < history_points_needed:
#             raise ValueError(f"历史数据不足（需要{history_points_needed}条，实际{len(history_data)}条）")
            
#         # 获取最近的历史数据
#         recent_history = history_data.iloc[-history_points_needed:]
        
#         # 准备预测结果
#         predictions = []
#         prediction_dates = []
        
#         # 获取最后一条数据的日期
#         last_date = recent_history['dateTime'].iloc[-1]
        
#         # 为每一天创建预测
#         for day in range(future_days):
#             # 计算预测日期
#             prediction_date = last_date + timedelta(days=day+1)
#             prediction_dates.append(prediction_date)
            
#             # 从天气数据中获取该日的天气特征
#             # 注意：这里假设weather_data包含未来日期的天气预测
#             weather_for_day = weather_data[
#                 (weather_data['dateTime'] >= prediction_date) & 
#                 (weather_data['dateTime'] < prediction_date + timedelta(days=1))
#             ]
            
#             if len(weather_for_day) == 0:
#                 print(f"警告：找不到{prediction_date.date()}的天气数据，使用最后一天的天气特征替代")
#                 weather_features = recent_history.iloc[-1][self.feature_cols].values
#             else:
#                 # 取当天第一个时间点的天气特征
#                 weather_features = weather_for_day.iloc[0][self.feature_cols].values
            
#             # 构建历史特征（不含负荷，因为是预测未来）
#             history_features = recent_history[self.feature_cols].values.flatten()
            
#             # 合并特征并预测
#             prediction_features = np.concatenate([history_features, weather_features])
#             prediction = self.model.predict([prediction_features])[0]
#             predictions.append(prediction)
            
#             # 更新历史数据（模拟滑动窗口）
#             # 创建一个新的行，包含预测日期和预测负荷
#             new_row = recent_history.iloc[-1].copy()
#             new_row['dateTime'] = prediction_date
#             new_row['load'] = prediction
#             # 其他特征使用当天天气数据填充
#             for col in self.feature_cols:
#                 if col in weather_for_day.columns:
#                     new_row[col] = weather_for_day.iloc[0][col]
            
#             # 更新最近历史数据（移除第一条，添加新预测的一条）
#             recent_history = pd.concat([recent_history.iloc[1:], pd.DataFrame([new_row])], ignore_index=True)
        
#         return pd.DataFrame({
#             'dateTime': prediction_dates,
#             'predicted_load': predictions
#         }).set_index('dateTime')


#     def _timedelta_to_freq(self, delta: pd.Timedelta) -> str:
#         """将时间差转换为pandas频率字符串"""
#         seconds = delta.total_seconds()
#         if seconds % 86400 == 0:
#             return f"{int(seconds // 86400)}D"  # 天
#         elif seconds % 3600 == 0:
#             return f"{int(seconds // 3600)}H"  # 小时
#         elif seconds % 60 == 0:
#             return f"{int(seconds // 60)}T"  # 分钟（pandas中T代表分钟）
#         else:
#             return f"{seconds}S"  # 秒


#     def save(self, path: str):
#         """保存模型（包含设备信息，加载时自动匹配）"""
#         model_state = {
#             'model': self.model,
#             'feature_cols': self.feature_cols,
#             'scaler': self.scaler,
#             'device': self.device,  # 保存训练时的设备
#             'random_state': self.random_state,
#             'history_days': self.history_days,  # 保存历史窗口大小
#             'freq': self.freq,  # 保存数据频率
#             'points_per_day': self.points_per_day  # 保存每天数据点数
#         }
#         joblib.dump(model_state, path)
#         print(f"模型已保存至: {path}（训练设备：{self.device}）")


#     def load(self, path: str):
#         """加载模型（自动适配当前设备）"""
#         model_state = joblib.load(path)
#         self.model = model_state['model']
#         self.feature_cols = model_state['feature_cols']
#         self.scaler = model_state['scaler']
#         self.random_state = model_state['random_state']
#         self.history_days = model_state['history_days']
#         self.freq = model_state['freq']
#         self.points_per_day = model_state['points_per_day']
        
#         # 加载时检查设备兼容性
#         saved_device = model_state['device']
#         current_device = self._resolve_device(saved_device)  # 重新解析设备
#         self.device = current_device
        
#         # 如果保存的是GPU模型但当前只有CPU，自动切换
#         if saved_device == 'cuda' and current_device == 'cpu':
#             print("⚠️ 加载的是GPU训练的模型，当前无GPU，将在CPU上运行（可能较慢）")
        
#         print(f"模型已从 {path} 加载（当前运行设备：{self.device}）")


from my_basic_model import MyBasicModel  
from tabpfn import TabPFNRegressor
import torch
import pandas as pd
import numpy as np
import time
import joblib  
# from datetime import timedelta  # 暂时不需要


class TabPfn(MyBasicModel):
    def __init__(self, device='auto', random_state=80, history_days=7):
        super().__init__()

        self.device = self._resolve_device(device)
        self.random_state = random_state
        self.history_days = history_days
        self.scaler = None
        self.feature_cols = None  # 原始特征列
        self.points_per_day = None  # 每天数据点数
        self.freq = None  # 数据频率

        self.model = self._default_model()


    def _resolve_device(self, device: str) -> str:
        if device == 'auto':
            return 'cuda' if torch.cuda.is_available() else 'cpu'
        elif device in ['cuda', 'cpu']:
            if device == 'cuda' and not torch.cuda.is_available():
                print("⚠️ 指定了'cuda'但未检测到GPU，自动切换到'cpu'")
                return 'cpu'
            return device
        else:
            print(f"⚠️ 无效设备参数'{device}'，使用'cpu'")
            return 'cpu'


    def _default_model(self) -> TabPFNRegressor:
        return TabPFNRegressor(
            random_state=self.random_state,
            device=self.device,
            ignore_pretraining_limits=True
        )


    def _timedelta_to_freq(self, delta: pd.Timedelta) -> str:
        """将时间差转换为频率字符串（如1小时→'1H'）"""
        seconds = delta.total_seconds()
        if seconds % 86400 == 0:
            return f"{int(seconds // 86400)}D"
        elif seconds % 3600 == 0:
            return f"{int(seconds // 3600)}H"
        elif seconds % 60 == 0:
            return f"{int(seconds // 60)}T"
        else:
            return f"{seconds}S"


    def init_from_data(self, time_series_data: pd.DataFrame, time_col='dateTime', feature_cols=None):
        """从包含时间列的原始数据中初始化频率和points_per_day"""
        # feature_cols参数保留用于兼容性，当前版本暂不使用
        _ = feature_cols  # 避免未使用警告

        if not time_series_data[time_col].is_monotonic_increasing:
            time_series_data = time_series_data.sort_values(time_col)
        if len(time_series_data) < 2:
            raise ValueError("初始化需要至少2条时间序列数据以解析频率")
        
        # 解析时间间隔
        time_delta = time_series_data[time_col].iloc[1] - time_series_data[time_col].iloc[0]
        self.freq = self._timedelta_to_freq(time_delta)
        
        # 计算每天点数
        if self.freq.endswith('H'):
            self.points_per_day = 24 // int(self.freq[:-1])
        elif self.freq.endswith('T'):
            self.points_per_day = 1440 // int(self.freq[:-1])  # 1440=24*60
        else:
            self.points_per_day = 1
        
        print(f"从数据初始化：频率={self.freq}，每天点数={self.points_per_day}")


    def train(self, X_train: pd.DataFrame, y_train: pd.Series):
        """TabPFN单日预测模式：使用endday前history_days天的数据训练，预测endday当天"""
        # 检查初始化状态
        if self.points_per_day is None:
            raise ValueError("请先调用init_from_data()初始化频率和每天点数，再进行训练")

        print(f"开始TabPFN单日预测训练（设备：{self.device}）")
        self.feature_cols = X_train.columns.tolist()

        # 计算每天的数据点数
        points_per_day = self.points_per_day
        history_points = self.history_days * points_per_day  # 历史天数对应的数据点数

        print(f"单日预测配置：history_days={self.history_days}, points_per_day={points_per_day}, history_points={history_points}")

        # 检查数据是否足够
        total_samples = len(X_train)
        if total_samples < history_points:
            raise ValueError(f"训练数据不足：需要至少{history_points}个样本，实际{total_samples}个")

        # 存储训练数据，用于预测时构建特征
        self.all_X_train = X_train.copy()
        self.all_y_train = y_train.copy()

        # 新的训练方式：使用历史时间点作为训练样本
        print("构建训练数据：每个历史时间点作为一个样本...")

        # 使用最近history_days天的所有时间点作为训练数据
        train_start_idx = max(0, total_samples - history_points)
        X_history = X_train.iloc[train_start_idx:total_samples]
        y_history = y_train.iloc[train_start_idx:total_samples]

        print(f"使用最近{self.history_days}天的数据：{len(X_history)}个时间点")
        print(f"每个样本特征数：{X_history.shape[1]}个（原始天气特征）")

        # 限制训练样本数以适应TabPFN
        max_train_samples = 1000
        if len(X_history) > max_train_samples:
            print(f"⚠️ 训练样本数({len(X_history)})超过TabPFN推荐值，随机采样{max_train_samples}个")
            indices = np.random.choice(len(X_history), max_train_samples, replace=False)
            X_final = X_history.iloc[indices]
            y_final = y_history.iloc[indices]
        else:
            X_final = X_history
            y_final = y_history

        # 数据清理：处理无穷大值和异常值
        print("🔧 开始数据清理...")
        X_values = np.array(X_final.values, dtype=float)
        y_values = np.array(y_final.values, dtype=float)

        # 检查并处理无穷大值
        inf_mask = np.isinf(X_values).any(axis=1)
        if inf_mask.any():
            print(f"⚠️ 发现{inf_mask.sum()}个包含无穷大值的样本，将被移除")
            valid_mask = ~inf_mask
            X_values = X_values[valid_mask]
            y_values = y_values[valid_mask]

        # 检查并处理NaN值
        nan_mask_x = np.isnan(X_values).any(axis=1)
        nan_mask_y = np.isnan(y_values)
        nan_mask = nan_mask_x | nan_mask_y
        if nan_mask.any():
            print(f"⚠️ 发现{nan_mask.sum()}个包含NaN值的样本，将被移除")
            valid_mask = ~nan_mask
            X_values = X_values[valid_mask]
            y_values = y_values[valid_mask]

        # 检查并处理过大的值
        large_mask = np.abs(X_values) > 1e10
        if large_mask.any():
            print(f"⚠️ 发现过大的值，将被截断到合理范围")
            X_values = np.clip(X_values, -1e10, 1e10)

        print(f"数据清理完成，剩余{len(X_values)}个有效样本")

        if len(X_values) == 0:
            raise ValueError("数据清理后没有有效样本，请检查数据质量")

        # 训练模型
        train_start = time.time()
        self.model.fit(X_values, y_values)

        train_time = time.time() - train_start
        print(f"TabPFN单日预测训练完成，耗时: {train_time:.2f} 秒")
        print(f"最终训练样本数: {len(X_final)}")
        print(f"特征维度: {X_final.shape[1]}")


    def predict(self, X_test: pd.DataFrame) -> pd.DataFrame:
        """单日预测模式：预测endday当天的96个负荷值"""
        if self.feature_cols is None:
            raise ValueError("模型尚未训练，请先调用train()")

        print(f"TabPFN单日预测开始：测试数据形状={X_test.shape}")

        # 检查测试数据是否为一天的数据（96个时间点）
        expected_points = self.points_per_day or 96  # 默认96个点
        if len(X_test) != expected_points:
            print(f"⚠️ 测试数据点数({len(X_test)})不等于一天的点数({expected_points})")
            print("将使用前96个点或补齐到96个点")

            if len(X_test) > expected_points:
                # 如果超过96个点，取前96个
                X_test = X_test.iloc[:expected_points]
            else:
                # 如果不足96个点，重复最后一行补齐
                last_row = X_test.iloc[-1:]
                while len(X_test) < expected_points:
                    X_test = pd.concat([X_test, last_row], ignore_index=True)

        X_test = X_test[self.feature_cols].ffill().bfill()  # 处理缺失值

        print(f"开始单日预测（设备：{self.device}），预测{len(X_test)}个时间点")
        pred_start = time.time()

        # 新的预测方式：直接使用18个天气特征（与训练方式一致）
        predictions: list[float] = []

        print(f"使用18个天气特征进行预测（与训练方式一致）")

        # 尝试批量预测以提高效率
        try:
            print("尝试批量预测...")
            X_test_array = np.array(X_test.values, dtype=float)
            predictions_batch = self.model.predict(X_test_array)
            predictions = [max(0, pred) for pred in predictions_batch]
            print(f"批量预测成功，预测了{len(predictions)}个时间点")
        except Exception as e:
            print(f"批量预测失败: {e}")
            print("回退到逐个预测...")

            # 回退到逐个预测
            for i in range(len(X_test)):
                print(f"预测第{i+1}/{len(X_test)}个时间点...")

                # 当前时间点的天气特征（18个）
                current_weather = np.array(X_test.iloc[i].values, dtype=float)

                # 预测（直接使用18个天气特征，与训练时一致）
                try:
                    pred = self.model.predict([current_weather])[0]
                    predictions.append(max(0, pred))  # 确保非负
                    print(f"  第{i+1}个时间点预测成功: {pred:.4f}")
                except Exception as e:
                    print(f"  第{i+1}个时间点预测失败: {e}")
                    # 使用默认值作为备用预测
                    predictions.append(float(0.15))  # 使用平均负荷作为备用

                if (i + 1) % 10 == 0:  # 每10个点输出一次进度
                    print(f"  已完成{i+1}/{len(X_test)}个时间点的预测")

        pred_time = time.time() - pred_start
        print(f"TabPFN单日预测完成，耗时: {pred_time:.2f} 秒")
        print(f"预测了{len(predictions)}个时间点")

        return pd.DataFrame({'value': predictions})


    # 未来预测功能已禁用（依赖已删除的聚合功能）.set_index('dateTime')


    def save(self, path: str):
        """保存模型（包含频率和点数信息）"""
        model_state = {
            'model': self.model,
            'feature_cols': self.feature_cols,
            'device': self.device,
            'random_state': self.random_state,
            'history_days': self.history_days,
            'freq': self.freq,
            'points_per_day': self.points_per_day,

        }
        joblib.dump(model_state, path)
        print(f"模型已保存至: {path}（设备：{self.device}）")


    def load(self, path: str):
        """加载模型并恢复状态"""
        model_state = joblib.load(path)
        self.model = model_state['model']
        self.feature_cols = model_state['feature_cols']
        self.random_state = model_state['random_state']
        self.history_days = model_state['history_days']
        self.freq = model_state['freq']
        self.points_per_day = model_state['points_per_day']

        
        # 设备适配
        self.device = self._resolve_device(model_state['device'])
        if model_state['device'] == 'cuda' and self.device == 'cpu':
            print("⚠️ 加载GPU模型，当前使用CPU运行")
        
        print(f"模型加载完成（设备：{self.device}）")