import matplotlib.pyplot as plt
import numpy as np
class TimeSeriesPlotter:
    """时间序列绘图工具类，用于绘制完整时序图和分段时序图"""
    
    def __init__(self, splitting_number=3):
        """
        初始化绘图工具
        :param splitting_number: 分段数量，默认为3段
        """
        self.splitting_number = splitting_number
        
        # 设置固定配置
        self.figsize = (18, 10)
        self.line_width = 1.5
        self.train_color = 'blue'
        self.test_color = 'green'
        self.pred_color = 'red'
        self.split_color = 'gray'
        self.grid_alpha = 0.3
        self.font_size = 12
        self.title_font_size = 16
    

    
    def plot_full_time_series(self, dates_train, y_train, train_pred, 
                             dates_test, y_test, test_pred, 
                             model_name, output_path):
        """
        绘制完整时序图（训练集+测试集）
        
        :param dates_train: 训练集日期
        :param y_train: 训练集真实值
        :param train_pred: 训练集预测值
        :param dates_test: 测试集日期
        :param y_test: 测试集真实值
        :param test_pred: 测试集预测值
        :param model_name: 模型名称
        :param output_path: 图片保存路径
        """
        # 设置中文字体
        plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'Arial Unicode MS']
        plt.rcParams['axes.unicode_minus'] = False
        
        # 创建画布
        plt.figure(figsize=self.figsize)

        # 绘制训练集真实值
        plt.plot(dates_train, y_train, label="训练集真实值",
                 color=self.train_color, linewidth=self.line_width)

        # 绘制训练集预测值
        plt.plot(dates_train, train_pred, label="训练集预测值",
                 color=self.train_color, linestyle="--", linewidth=self.line_width)

        # 绘制测试集真实值
        plt.plot(dates_test, y_test, label="测试集真实值",
                 color=self.test_color, linewidth=self.line_width)

        # 绘制测试集预测值
        plt.plot(dates_test, test_pred, label="测试集预测值",
                 color=self.pred_color, linestyle="--", linewidth=self.line_width)

        # 添加训练集与测试集分隔线
        if dates_train and dates_test:
            split_date = dates_train[-1] if len(dates_train) > 0 else dates_test[0]
            plt.axvline(x=split_date, color=self.split_color,
                        linestyle=':', linewidth=2, label="训练集/测试集分割线")

        # 设置图表属性
        plt.title(f"{model_name} 模型完整时序预测对比（训练集+测试集）",
                 fontsize=self.title_font_size, fontweight='bold')
        plt.xlabel("时间", fontsize=self.font_size)
        plt.ylabel("负荷值", fontsize=self.font_size)
        plt.legend(fontsize=self.font_size)
        plt.grid(True, alpha=self.grid_alpha)
        plt.xticks(rotation=45)
        plt.tight_layout()
        
        # 保存完整时序图
        plt.savefig(output_path, dpi=300, bbox_inches='tight')
        plt.close()
        print(f"完整时序对比图已保存至: {output_path}")
    
    def plot_split_time_series(self, dates_train, y_train, train_pred,
                              dates_test, y_test, test_pred,
                              model_name, output_path):
        """
        绘制分段时序图（将完整时序分成指定块数展示）

        :param dates_train: 训练集日期
        :param y_train: 训练集真实值
        :param train_pred: 训练集预测值
        :param dates_test: 测试集日期
        :param y_test: 测试集真实值
        :param test_pred: 测试集预测值
        :param model_name: 模型名称
        :param output_path: 图片保存路径
        """
        splitting_number = self.splitting_number
        # 设置中文字体
        plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'Arial Unicode MS']
        plt.rcParams['axes.unicode_minus'] = False
        
        # 合并训练集和测试集数据
        all_dates = np.concatenate([dates_train, dates_test])
        all_actual = np.concatenate([y_train, y_test])
        all_predicted = np.concatenate([train_pred, test_pred])
        
        # 计算分割点
        total_len = len(all_dates)
        split_points = [int(total_len * i / splitting_number) for i in range(1, splitting_number)]
        
        # 动态调整画布高度（每块约5英寸）
        fig_height = min(30, splitting_number * 5)  # 最大高度限制为30英寸
        figsize = (self.figsize[0], fig_height)

        # 创建包含多个子图的画布
        fig, axes = plt.subplots(splitting_number, 1,
                                figsize=figsize,
                                sharey=True,
                                gridspec_kw={'hspace': 0.15})
        
        # 如果只有一个子图，axes不是数组，需要转换为数组形式处理
        if splitting_number == 1:
            axes = [axes]
        
        # 计算训练集结束点在合并后数据中的位置
        train_end_idx = len(dates_train)
        
        # 绘制每个子图
        start_idx = 0
        for i, split_idx in enumerate(split_points + [total_len]):
            ax = axes[i]
            
            # 绘制当前段的真实值和预测值
            ax.plot(all_dates[start_idx:split_idx], all_actual[start_idx:split_idx],
                    label="真实值", color=self.train_color, linewidth=self.line_width)
            ax.plot(all_dates[start_idx:split_idx], all_predicted[start_idx:split_idx],
                    label="预测值", color=self.pred_color, linestyle="--", linewidth=self.line_width)

            # 如果当前段包含训练集与测试集的分割点，添加分割线
            if start_idx < train_end_idx < split_idx:
                ax.axvline(x=dates_train[-1], color=self.split_color,
                          linestyle=':', linewidth=2, label="训练集/测试集分割线")

            # 设置子图标题
            ax.set_title(f"时间序列预测对比（第{i+1}/{splitting_number}部分）",
                        fontsize=self.title_font_size-2)
            ax.grid(True, alpha=self.grid_alpha)
            ax.tick_params(axis='x', rotation=45)

            # 只在第一个子图显示图例
            if i == 0:
                ax.legend(fontsize=self.font_size-1)
            
            start_idx = split_idx
        
        # 设置整体标题和坐标轴标签
        fig.suptitle(f"{model_name} 模型时序预测对比（分{splitting_number}段展示）",
                    fontsize=self.title_font_size+2, fontweight='bold')
        fig.text(0.04, 0.5, '负荷值', va='center', rotation='vertical', fontsize=self.font_size)
        fig.text(0.5, 0.04, '时间', ha='center', fontsize=self.font_size)
        
        # 调整布局
        plt.tight_layout(rect=(0.05, 0.05, 1, 0.95))  # 预留suptitle的空间
        
        # 保存分割后的时序图
        plt.savefig(output_path, dpi=300, bbox_inches='tight')
        plt.close()
        print(f"分割时序对比图已保存至: {output_path}")

    def plot_test_only_comparison(self, dates_test, y_test, test_pred, model_name, output_path):
        """
        绘制单独的测试集对比图

        :param dates_test: 测试集日期
        :param y_test: 测试集真实值
        :param test_pred: 测试集预测值
        :param model_name: 模型名称
        :param output_path: 图片保存路径
        """
        # 设置中文字体
        plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'Arial Unicode MS']
        plt.rcParams['axes.unicode_minus'] = False

        # 创建画布
        plt.figure(figsize=(15, 8))

        # 绘制测试集真实值
        plt.plot(dates_test, y_test, label="真实值", color="blue", linewidth=2, marker='o', markersize=3)

        # 绘制测试集预测值
        plt.plot(dates_test, test_pred, label="预测值", color="red", linewidth=2, linestyle="--", marker='s', markersize=3)

        # 设置图表属性
        plt.title(f"{model_name} 模型测试集预测对比", fontsize=16, fontweight='bold')
        plt.xlabel("时间", fontsize=12)
        plt.ylabel("负荷值", fontsize=12)
        plt.legend(fontsize=12)
        plt.grid(True, alpha=0.3)
        plt.xticks(rotation=45)
        plt.tight_layout()

        # 保存测试集对比图
        plt.savefig(output_path, dpi=300, bbox_inches='tight')
        plt.close()
        print(f"测试集对比图已保存至: {output_path}")

    def plot_all_charts_and_calculate_metrics(self, dates_train, y_train, train_pred,
                                            dates_test, y_test, test_pred, model_name):
        """
        一次性生成所有图表并计算评估指标

        :param dates_train: 训练集日期
        :param y_train: 训练集真实值
        :param train_pred: 训练集预测值
        :param dates_test: 测试集日期
        :param y_test: 测试集真实值
        :param test_pred: 测试集预测值
        :param model_name: 模型名称
        :return: 评估指标字典
        """
        # 1. 完整时序图
        full_plot_path = f"{model_name}_full_time_series.png"
        self.plot_full_time_series(
            dates_train, y_train, train_pred,
            dates_test, y_test, test_pred,
            model_name, full_plot_path
        )

        # 2. 分段时序图
        split_plot_path = f"{model_name}_split_time_series_{self.splitting_number}segments.png"
        self.plot_split_time_series(
            dates_train, y_train, train_pred,
            dates_test, y_test, test_pred,
            model_name, split_plot_path
        )

        # 3. 测试集对比图
        test_plot_path = f"{model_name}_test_comparison.png"
        self.plot_test_only_comparison(
            dates_test, y_test, test_pred,
            model_name, test_plot_path
        )

        # 4. 计算并返回评估指标
        y_test_array = np.array(y_test)
        test_pred_array = np.array(test_pred)

        mae = np.mean(np.abs(y_test_array - test_pred_array))
        rmse = np.sqrt(np.mean((y_test_array - test_pred_array) ** 2))

        # 避免除零错误
        nonzero_mask = y_test_array != 0
        if np.sum(nonzero_mask) > 0:
            mape = np.mean(np.abs((y_test_array[nonzero_mask] - test_pred_array[nonzero_mask]) / y_test_array[nonzero_mask]) * 100)
        else:
            mape = float('inf')

        metrics = {
            'mae': mae,
            'rmse': rmse,
            'mape': mape
        }

        # 打印评估指标
        print(f"\n测试集详细评估指标:")
        print(f"- MAE (平均绝对误差): {mae:.4f}")
        print(f"- RMSE (均方根误差): {rmse:.4f}")
        print(f"- MAPE (平均绝对百分比误差): {mape:.2f}%" if mape != float('inf') else "- MAPE: 无法计算（存在零值）")

        return metrics

    def plot_raw_load_data(self, df_load, output_path="raw_load_data.png"):
        """
        绘制原始负荷数据时间序列图

        :param df_load: 负荷数据DataFrame，包含'dateTime'和'load'列
        :param output_path: 图片保存路径
        """
        # 设置中文字体
        plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'Arial Unicode MS']
        plt.rcParams['axes.unicode_minus'] = False

        # 创建画布
        plt.figure(figsize=(16, 8))

        # 绘制负荷数据
        plt.plot(df_load['dateTime'], df_load['load'],
                color='steelblue', linewidth=1.5, alpha=0.8)

        # 设置图表属性
        plt.title("原始负荷数据时间序列", fontsize=16, fontweight='bold', pad=20)
        plt.xlabel("时间", fontsize=12)
        plt.ylabel("负荷值 (kW)", fontsize=12)
        plt.grid(True, alpha=0.3, linestyle='--')
        plt.xticks(rotation=45)

        # 添加统计信息
        load_mean = df_load['load'].mean()
        load_max = df_load['load'].max()
        load_min = df_load['load'].min()

        # 在图上添加统计信息文本
        stats_text = f"统计信息:\n平均值: {load_mean:.2f} kW\n最大值: {load_max:.2f} kW\n最小值: {load_min:.2f} kW\n数据点数: {len(df_load)}"
        plt.text(0.02, 0.98, stats_text, transform=plt.gca().transAxes,
                fontsize=10, verticalalignment='top',
                bbox=dict(boxstyle='round', facecolor='wheat', alpha=0.8))

        # 调整布局
        plt.tight_layout()

        # 保存图片
        plt.savefig(output_path, dpi=300, bbox_inches='tight')
        plt.close()
        print(f"原始负荷数据图已保存至: {output_path}")

        return {
            'mean': load_mean,
            'max': load_max,
            'min': load_min,
            'count': len(df_load)
        }