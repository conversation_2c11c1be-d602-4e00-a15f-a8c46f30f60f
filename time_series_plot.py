import matplotlib.pyplot as plt
import numpy as np
class TimeSeriesPlotter:
    """时间序列绘图工具类，用于绘制完整时序图和分段时序图"""
    
    def __init__(self, config=None):
        """
        初始化绘图工具
        :param config: 配置字典，可包含图片大小、颜色等参数
        """
        self.config = config or {}
        
        # 设置默认配置
        self.defaults = {
            'figsize': (18, 10),  # 画布大小
            'line_width': 1.5,    # 线条宽度
            'train_color': 'blue',  # 训练集颜色
            'test_color': 'green',  # 测试集颜色
            'pred_color': 'red',    # 预测值颜色
            'split_color': 'gray',  # 分割线颜色
            'grid_alpha': 0.3,      # 网格透明度
            'font_size': 12,        # 字体大小
            'title_font_size': 16,  # 标题字体大小
            'subplots': {
                'sharey': True,     # 共享y轴
                'hspace': 0.15      # 子图间距
            }
        }
        
        # 合并用户配置和默认配置
        self._update_config()
    
    def _update_config(self):
        """合并用户配置和默认配置"""
        from copy import deepcopy
        self.cfg = deepcopy(self.defaults)
        
        # 更新顶层配置
        for key, value in self.config.items():
            if key in self.defaults:
                self.cfg[key] = value
        
        # 更新子配置
        if 'subplots' in self.config:
            for key, value in self.config['subplots'].items():
                if key in self.defaults['subplots']:
                    self.cfg['subplots'][key] = value
    
    def plot_full_time_series(self, dates_train, y_train, train_pred, 
                             dates_test, y_test, test_pred, 
                             model_name, output_path):
        """
        绘制完整时序图（训练集+测试集）
        
        :param dates_train: 训练集日期
        :param y_train: 训练集真实值
        :param train_pred: 训练集预测值
        :param dates_test: 测试集日期
        :param y_test: 测试集真实值
        :param test_pred: 测试集预测值
        :param model_name: 模型名称
        :param output_path: 图片保存路径
        """
        # 设置中文字体
        plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'Arial Unicode MS']
        plt.rcParams['axes.unicode_minus'] = False
        
        # 创建画布
        plt.figure(figsize=self.cfg['figsize'])
        
        # 绘制训练集真实值
        plt.plot(dates_train, y_train, label="训练集真实值", 
                 color=self.cfg['train_color'], linewidth=self.cfg['line_width'])
        
        # 绘制训练集预测值
        plt.plot(dates_train, train_pred, label="训练集预测值", 
                 color=self.cfg['train_color'], linestyle="--", linewidth=self.cfg['line_width'])
        
        # 绘制测试集真实值
        plt.plot(dates_test, y_test, label="测试集真实值", 
                 color=self.cfg['test_color'], linewidth=self.cfg['line_width'])
        
        # 绘制测试集预测值
        plt.plot(dates_test, test_pred, label="测试集预测值", 
                 color=self.cfg['pred_color'], linestyle="--", linewidth=self.cfg['line_width'])
        
        # 添加训练集与测试集分隔线
        if dates_train and dates_test:
            split_date = dates_train[-1] if len(dates_train) > 0 else dates_test[0]
            plt.axvline(x=split_date, color=self.cfg['split_color'], 
                        linestyle=':', linewidth=2, label="训练集/测试集分割线")
        
        # 设置图表属性
        plt.title(f"{model_name} 模型完整时序预测对比（训练集+测试集）", 
                 fontsize=self.cfg['title_font_size'], fontweight='bold')
        plt.xlabel("时间", fontsize=self.cfg['font_size'])
        plt.ylabel("负荷值", fontsize=self.cfg['font_size'])
        plt.legend(fontsize=self.cfg['font_size'])
        plt.grid(True, alpha=self.cfg['grid_alpha'])
        plt.xticks(rotation=45)
        plt.tight_layout()
        
        # 保存完整时序图
        plt.savefig(output_path, dpi=300, bbox_inches='tight')
        plt.close()
        print(f"完整时序对比图已保存至: {output_path}")
    
    def plot_split_time_series(self, dates_train, y_train, train_pred, 
                              dates_test, y_test, test_pred, 
                              model_name, output_path, splitting_number=3):
        """
        绘制分段时序图（将完整时序分成指定块数展示）
        
        :param dates_train: 训练集日期
        :param y_train: 训练集真实值
        :param train_pred: 训练集预测值
        :param dates_test: 测试集日期
        :param y_test: 测试集真实值
        :param test_pred: 测试集预测值
        :param model_name: 模型名称
        :param output_path: 图片保存路径
        :param splitting_number: 分割块数，默认为3
        """
        # 设置中文字体
        plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'Arial Unicode MS']
        plt.rcParams['axes.unicode_minus'] = False
        
        # 合并训练集和测试集数据
        all_dates = np.concatenate([dates_train, dates_test])
        all_actual = np.concatenate([y_train, y_test])
        all_predicted = np.concatenate([train_pred, test_pred])
        
        # 计算分割点
        total_len = len(all_dates)
        split_points = [int(total_len * i / splitting_number) for i in range(1, splitting_number)]
        
        # 动态调整画布高度（每块约5英寸）
        fig_height = min(30, splitting_number * 5)  # 最大高度限制为30英寸
        figsize = (self.cfg['figsize'][0], fig_height)
        
        # 创建包含多个子图的画布
        fig, axes = plt.subplots(splitting_number, 1, 
                                figsize=figsize,
                                sharey=self.cfg['subplots']['sharey'],
                                gridspec_kw={'hspace': self.cfg['subplots']['hspace']})
        
        # 如果只有一个子图，axes不是数组，需要转换为数组形式处理
        if splitting_number == 1:
            axes = [axes]
        
        # 计算训练集结束点在合并后数据中的位置
        train_end_idx = len(dates_train)
        
        # 绘制每个子图
        start_idx = 0
        for i, split_idx in enumerate(split_points + [total_len]):
            ax = axes[i]
            
            # 绘制当前段的真实值和预测值
            ax.plot(all_dates[start_idx:split_idx], all_actual[start_idx:split_idx], 
                    label="真实值", color=self.cfg['train_color'], linewidth=self.cfg['line_width'])
            ax.plot(all_dates[start_idx:split_idx], all_predicted[start_idx:split_idx], 
                    label="预测值", color=self.cfg['pred_color'], linestyle="--", linewidth=self.cfg['line_width'])
            
            # 如果当前段包含训练集与测试集的分割点，添加分割线
            if start_idx < train_end_idx < split_idx:
                ax.axvline(x=dates_train[-1], color=self.cfg['split_color'], 
                          linestyle=':', linewidth=2, label="训练集/测试集分割线")
            
            # 设置子图标题
            ax.set_title(f"时间序列预测对比（第{i+1}/{splitting_number}部分）", 
                        fontsize=self.cfg['title_font_size']-2)
            ax.grid(True, alpha=self.cfg['grid_alpha'])
            ax.tick_params(axis='x', rotation=45)
            
            # 只在第一个子图显示图例
            if i == 0:
                ax.legend(fontsize=self.cfg['font_size']-1)
            
            start_idx = split_idx
        
        # 设置整体标题和坐标轴标签
        fig.suptitle(f"{model_name} 模型时序预测对比（分{splitting_number}段展示）", 
                    fontsize=self.cfg['title_font_size']+2, fontweight='bold')
        fig.text(0.04, 0.5, '负荷值', va='center', rotation='vertical', fontsize=self.cfg['font_size'])
        fig.text(0.5, 0.04, '时间', ha='center', fontsize=self.cfg['font_size'])
        
        # 调整布局
        plt.tight_layout(rect=[0.05, 0.05, 1, 0.95])  # 预留suptitle的空间
        
        # 保存分割后的时序图
        plt.savefig(output_path, dpi=300, bbox_inches='tight')
        plt.close()
        print(f"分割时序对比图已保存至: {output_path}")