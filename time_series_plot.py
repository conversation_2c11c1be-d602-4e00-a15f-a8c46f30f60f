import matplotlib.pyplot as plt
import numpy as np
import pandas as pd
class TimeSeriesPlotter:
    """时间序列绘图工具类，用于绘制完整时序图和分段时序图"""
    
    def __init__(self, splitting_number=3):
        """
        初始化绘图工具
        :param splitting_number: 分段数量，默认为3段
        """
        self.splitting_number = splitting_number
        
        # 设置固定配置
        self.figsize = (18, 10)
        self.line_width = 1.5
        self.train_color = 'blue'
        self.test_color = 'green'
        self.pred_color = 'red'
        self.split_color = 'gray'
        self.grid_alpha = 0.3
        self.font_size = 12
        self.title_font_size = 16
    

    
    def plot_full_time_series(self, dates_train, y_train, train_pred, 
                             dates_test, y_test, test_pred, 
                             model_name, output_path):
        """
        绘制完整时序图（训练集+测试集）
        
        :param dates_train: 训练集日期
        :param y_train: 训练集真实值
        :param train_pred: 训练集预测值
        :param dates_test: 测试集日期
        :param y_test: 测试集真实值
        :param test_pred: 测试集预测值
        :param model_name: 模型名称
        :param output_path: 图片保存路径
        """
        # 设置中文字体
        plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'Arial Unicode MS']
        plt.rcParams['axes.unicode_minus'] = False
        
        # 创建画布
        plt.figure(figsize=self.figsize)

        # 绘制训练集真实值
        plt.plot(dates_train, y_train, label="训练集真实值",
                 color=self.train_color, linewidth=self.line_width)

        # 绘制训练集预测值
        plt.plot(dates_train, train_pred, label="训练集预测值",
                 color=self.train_color, linestyle="--", linewidth=self.line_width)

        # 绘制测试集真实值
        plt.plot(dates_test, y_test, label="测试集真实值",
                 color=self.test_color, linewidth=self.line_width)

        # 绘制测试集预测值
        plt.plot(dates_test, test_pred, label="测试集预测值",
                 color=self.pred_color, linestyle="--", linewidth=self.line_width)

        # 添加训练集与测试集分隔线
        if dates_train and dates_test:
            split_date = dates_train[-1] if len(dates_train) > 0 else dates_test[0]
            plt.axvline(x=split_date, color=self.split_color,
                        linestyle=':', linewidth=2, label="训练集/测试集分割线")

        # 设置图表属性
        plt.title(f"{model_name} 模型完整时序预测对比（训练集+测试集）",
                 fontsize=self.title_font_size, fontweight='bold')
        plt.xlabel("时间", fontsize=self.font_size)
        plt.ylabel("负荷值", fontsize=self.font_size)
        plt.legend(fontsize=self.font_size)
        plt.grid(True, alpha=self.grid_alpha)
        plt.xticks(rotation=45)
        plt.tight_layout()
        
        # 保存完整时序图
        plt.savefig(output_path, dpi=300, bbox_inches='tight')
        plt.close()
        print(f"完整时序对比图已保存至: {output_path}")
    
    def plot_split_time_series(self, dates_train, y_train, train_pred,
                              dates_test, y_test, test_pred,
                              model_name, output_path):
        """
        绘制分段时序图（将完整时序分成指定块数展示）

        :param dates_train: 训练集日期
        :param y_train: 训练集真实值
        :param train_pred: 训练集预测值
        :param dates_test: 测试集日期
        :param y_test: 测试集真实值
        :param test_pred: 测试集预测值
        :param model_name: 模型名称
        :param output_path: 图片保存路径
        """
        splitting_number = self.splitting_number
        # 设置中文字体
        plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'Arial Unicode MS']
        plt.rcParams['axes.unicode_minus'] = False
        
        # 合并训练集和测试集数据
        all_dates = np.concatenate([dates_train, dates_test])
        all_actual = np.concatenate([y_train, y_test])
        all_predicted = np.concatenate([train_pred, test_pred])
        
        # 计算分割点
        total_len = len(all_dates)
        split_points = [int(total_len * i / splitting_number) for i in range(1, splitting_number)]
        
        # 动态调整画布高度（每块约5英寸）
        fig_height = min(30, splitting_number * 5)  # 最大高度限制为30英寸
        figsize = (self.figsize[0], fig_height)

        # 创建包含多个子图的画布
        fig, axes = plt.subplots(splitting_number, 1,
                                figsize=figsize,
                                sharey=True,
                                gridspec_kw={'hspace': 0.15})
        
        # 如果只有一个子图，axes不是数组，需要转换为数组形式处理
        if splitting_number == 1:
            axes = [axes]
        
        # 计算训练集结束点在合并后数据中的位置
        train_end_idx = len(dates_train)
        
        # 绘制每个子图
        start_idx = 0
        for i, split_idx in enumerate(split_points + [total_len]):
            ax = axes[i]
            
            # 绘制当前段的真实值和预测值
            ax.plot(all_dates[start_idx:split_idx], all_actual[start_idx:split_idx],
                    label="真实值", color=self.train_color, linewidth=self.line_width)
            ax.plot(all_dates[start_idx:split_idx], all_predicted[start_idx:split_idx],
                    label="预测值", color=self.pred_color, linestyle="--", linewidth=self.line_width)

            # 如果当前段包含训练集与测试集的分割点，添加分割线
            if start_idx < train_end_idx < split_idx:
                ax.axvline(x=dates_train[-1], color=self.split_color,
                          linestyle=':', linewidth=2, label="训练集/测试集分割线")

            # 设置子图标题
            ax.set_title(f"时间序列预测对比（第{i+1}/{splitting_number}部分）",
                        fontsize=self.title_font_size-2)
            ax.grid(True, alpha=self.grid_alpha)
            ax.tick_params(axis='x', rotation=45)

            # 只在第一个子图显示图例
            if i == 0:
                ax.legend(fontsize=self.font_size-1)
            
            start_idx = split_idx
        
        # 设置整体标题和坐标轴标签
        fig.suptitle(f"{model_name} 模型时序预测对比（分{splitting_number}段展示）",
                    fontsize=self.title_font_size+2, fontweight='bold')
        fig.text(0.04, 0.5, '负荷值', va='center', rotation='vertical', fontsize=self.font_size)
        fig.text(0.5, 0.04, '时间', ha='center', fontsize=self.font_size)
        
        # 调整布局
        plt.tight_layout(rect=(0.05, 0.05, 1, 0.95))  # 预留suptitle的空间
        
        # 保存分割后的时序图
        plt.savefig(output_path, dpi=300, bbox_inches='tight')
        plt.close()
        print(f"分割时序对比图已保存至: {output_path}")

    def plot_test_only_comparison(self, dates_test, y_test, test_pred, model_name, output_path):
        """
        绘制单独的测试集对比图

        :param dates_test: 测试集日期
        :param y_test: 测试集真实值
        :param test_pred: 测试集预测值
        :param model_name: 模型名称
        :param output_path: 图片保存路径
        """
        # 设置中文字体
        plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'Arial Unicode MS']
        plt.rcParams['axes.unicode_minus'] = False

        # 创建画布
        plt.figure(figsize=(15, 8))

        # 绘制测试集真实值
        plt.plot(dates_test, y_test, label="真实值", color="blue", linewidth=2, marker='o', markersize=3)

        # 绘制测试集预测值
        plt.plot(dates_test, test_pred, label="预测值", color="red", linewidth=2, linestyle="--", marker='s', markersize=3)

        # 设置图表属性
        plt.title(f"{model_name} 模型测试集预测对比", fontsize=16, fontweight='bold')
        plt.xlabel("时间", fontsize=12)
        plt.ylabel("负荷值", fontsize=12)
        plt.legend(fontsize=12)
        plt.grid(True, alpha=0.3)
        plt.xticks(rotation=45)
        plt.tight_layout()

        # 保存测试集对比图
        plt.savefig(output_path, dpi=300, bbox_inches='tight')
        plt.close()
        print(f"测试集对比图已保存至: {output_path}")

    def plot_all_charts_and_calculate_metrics(self, dates_train, y_train, train_pred,
                                            dates_test, y_test, test_pred, model_name):
        """
        一次性生成所有图表并计算评估指标

        :param dates_train: 训练集日期
        :param y_train: 训练集真实值
        :param train_pred: 训练集预测值
        :param dates_test: 测试集日期
        :param y_test: 测试集真实值
        :param test_pred: 测试集预测值
        :param model_name: 模型名称
        :return: 评估指标字典
        """
        # 1. 完整时序图
        full_plot_path = f"{model_name}_full_time_series.png"
        self.plot_full_time_series(
            dates_train, y_train, train_pred,
            dates_test, y_test, test_pred,
            model_name, full_plot_path
        )

        # 2. 分段时序图
        split_plot_path = f"{model_name}_split_time_series_{self.splitting_number}segments.png"
        self.plot_split_time_series(
            dates_train, y_train, train_pred,
            dates_test, y_test, test_pred,
            model_name, split_plot_path
        )

        # 3. 测试集对比图
        test_plot_path = f"{model_name}_test_comparison.png"
        self.plot_test_only_comparison(
            dates_test, y_test, test_pred,
            model_name, test_plot_path
        )

        # 4. 计算并返回评估指标
        y_test_array = np.array(y_test)
        test_pred_array = np.array(test_pred)

        mae = np.mean(np.abs(y_test_array - test_pred_array))
        rmse = np.sqrt(np.mean((y_test_array - test_pred_array) ** 2))

        # 避免除零错误
        nonzero_mask = y_test_array != 0
        if np.sum(nonzero_mask) > 0:
            mape = np.mean(np.abs((y_test_array[nonzero_mask] - test_pred_array[nonzero_mask]) / y_test_array[nonzero_mask]) * 100)
        else:
            mape = float('inf')

        metrics = {
            'mae': mae,
            'rmse': rmse,
            'mape': mape
        }

        # 打印评估指标
        print(f"\n测试集详细评估指标:")
        print(f"- MAE (平均绝对误差): {mae:.4f}")
        print(f"- RMSE (均方根误差): {rmse:.4f}")
        print(f"- MAPE (平均绝对百分比误差): {mape:.2f}%" if mape != float('inf') else "- MAPE: 无法计算（存在零值）")

        return metrics

    def plot_raw_load_data(self, df_load, preprocessor=None, output_path="raw_load_data.png"):
        """
        绘制原始负荷数据时间序列图，标出周末和节假日区域

        :param df_load: 负荷数据DataFrame，包含'dateTime'和'load'列
        :param preprocessor: DataPreprocessor实例，用于获取节假日信息
        :param output_path: 图片保存路径
        """
        # 设置中文字体
        plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'Arial Unicode MS']
        plt.rcParams['axes.unicode_minus'] = False

        # 创建画布
        plt.figure(figsize=(16, 8))

        # 如果提供了preprocessor，添加周末和节假日背景
        if preprocessor is not None:
            self._add_weekend_holiday_background(df_load, preprocessor)

        # 绘制负荷数据
        plt.plot(df_load['dateTime'], df_load['load'],
                color='steelblue', linewidth=1.5, alpha=0.9, zorder=3)

        # 设置图表属性
        plt.title("原始负荷数据时间序列（含周末节假日标识）", fontsize=16, fontweight='bold', pad=20)
        plt.xlabel("时间", fontsize=12)
        plt.ylabel("负荷值 (kW)", fontsize=12)
        plt.grid(True, alpha=0.3, linestyle='--', zorder=1)
        plt.xticks(rotation=45)

        # 添加统计信息
        load_mean = df_load['load'].mean()
        load_max = df_load['load'].max()
        load_min = df_load['load'].min()

        # 在图上添加统计信息文本
        stats_text = f"统计信息:\n平均值: {load_mean:.2f} kW\n最大值: {load_max:.2f} kW\n最小值: {load_min:.2f} kW\n数据点数: {len(df_load)}"
        plt.text(0.02, 0.98, stats_text, transform=plt.gca().transAxes,
                fontsize=10, verticalalignment='top',
                bbox=dict(boxstyle='round', facecolor='wheat', alpha=0.8), zorder=4)

        # 添加图例（如果有背景标识）
        if preprocessor is not None:
            from matplotlib.patches import Patch
            from matplotlib.lines import Line2D
            legend_elements = [
                Patch(facecolor='lightcoral', alpha=0.3, label='周末'),
                Patch(facecolor='lightblue', alpha=0.4, label='法定节假日'),
                Line2D([0], [0], color='steelblue', linewidth=1.5, label='负荷数据')
            ]
            plt.legend(handles=legend_elements, loc='upper right', fontsize=10)

        # 调整布局
        plt.tight_layout()

        # 保存图片
        plt.savefig(output_path, dpi=300, bbox_inches='tight')
        plt.close()
        print(f"原始负荷数据图已保存至: {output_path}")

        return {
            'mean': load_mean,
            'max': load_max,
            'min': load_min,
            'count': len(df_load)
        }

    def _add_weekend_holiday_background(self, df_load, preprocessor):
        """
        添加周末和节假日背景区域

        :param df_load: 负荷数据DataFrame
        :param preprocessor: DataPreprocessor实例
        """
        # 获取时间范围
        start_date = df_load['dateTime'].min().strftime('%Y-%m-%d')
        end_date = df_load['dateTime'].max().strftime('%Y-%m-%d')

        # 创建临时DataFrame用于获取节假日信息
        temp_df = pd.DataFrame({'dateTime': df_load['dateTime']})
        temp_df['date'] = temp_df['dateTime'].dt.date.astype(str)

        # 使用preprocessor的节假日特征方法
        temp_df = preprocessor._with_holiday_features(temp_df, start_date, end_date)

        # 添加真正的节假日标识
        temp_df = self._add_real_holidays(temp_df)

        # 标识周末和节假日区域
        current_date = None
        current_type = None
        start_time = None

        for idx, row in temp_df.iterrows():
            date_str = row['date']
            day_type = row['day_type']
            is_real_holiday = row.get('is_real_holiday', False)
            datetime_val = row['dateTime']

            # 判断是否为新的一天
            if current_date != date_str:
                # 如果前一天有特殊类型，结束前一天的区域
                if current_date is not None and (current_type in [6, 7] or current_is_holiday):
                    end_time = temp_df.iloc[idx-1]['dateTime'] + pd.Timedelta(minutes=15)
                    self._fill_day_background(start_time, end_time, current_type, current_is_holiday)

                # 开始新的一天
                current_date = date_str
                current_type = day_type
                current_is_holiday = is_real_holiday
                if day_type in [6, 7] or is_real_holiday:  # 周末或节假日
                    start_time = datetime_val

        # 处理最后一天
        if current_type in [6, 7] or current_is_holiday:
            end_time = temp_df.iloc[-1]['dateTime'] + pd.Timedelta(minutes=15)
            self._fill_day_background(start_time, end_time, current_type, current_is_holiday)

    def _add_real_holidays(self, temp_df):
        """
        添加真正的法定节假日标识

        :param temp_df: 包含日期信息的DataFrame
        :return: 添加了节假日标识的DataFrame
        """
        # 2025年中国法定节假日（示例）
        holidays_2025 = [
            # 元旦
            '2025-01-01',
            # 春节
            '2025-01-28', '2025-01-29', '2025-01-30', '2025-01-31',
            '2025-02-01', '2025-02-02', '2025-02-03',
            # 清明节
            '2025-04-05', '2025-04-06', '2025-04-07',
            # 劳动节
            '2025-05-01', '2025-05-02', '2025-05-03',
            # 端午节
            '2025-05-31', '2025-06-02',
            # 中秋节
            '2025-10-06', '2025-10-07', '2025-10-08',
            # 国庆节
            '2025-10-01', '2025-10-02', '2025-10-03', '2025-10-04',
            '2025-10-05', '2025-10-06', '2025-10-07'
        ]

        # 标记真正的节假日
        temp_df['is_real_holiday'] = temp_df['date'].isin(holidays_2025)

        return temp_df

    def _fill_day_background(self, start_time, end_time, day_type, is_real_holiday=False):
        """
        填充单日的背景色

        :param start_time: 开始时间
        :param end_time: 结束时间
        :param day_type: 日期类型 (6=周六, 7=周日, 1=工作日)
        :param is_real_holiday: 是否为法定节假日
        """
        if is_real_holiday:  # 法定节假日优先
            color = 'lightblue'
            alpha = 0.4
        elif day_type == 6:  # 周六
            color = 'lightcoral'
            alpha = 0.2
        elif day_type == 7:  # 周日
            color = 'lightcoral'
            alpha = 0.3
        else:
            return  # 工作日不填充

        # 使用axvspan填充垂直区域
        plt.axvspan(start_time, end_time, color=color, alpha=alpha, zorder=2)