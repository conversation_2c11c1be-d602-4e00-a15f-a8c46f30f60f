import numpy as np
import pandas as pd
from sklearn.ensemble import <PERSON><PERSON><PERSON><PERSON><PERSON>tingRegressor
from sklearn.preprocessing import StandardScaler
from sklearn.metrics import mean_squared_error, mean_absolute_error
import time
import joblib
from my_basic_model import MyBasicModel


class XGBoostModel(MyBasicModel):
    """梯度提升时间序列预测模型（使用sklearn GradientBoosting替代XGBoost）"""
    
    def __init__(self, n_estimators=500, max_depth=6, learning_rate=0.1,
                 subsample=0.8, random_state=42, feature_engineering=True,
                 lag_features=True):
        """
        初始化梯度提升模型

        参数:
            n_estimators: 树的数量
            max_depth: 树的最大深度
            learning_rate: 学习率
            subsample: 样本采样比例
            random_state: 随机种子
            feature_engineering: 是否进行特征工程
            lag_features: 是否添加滞后特征
        """
        super().__init__()

        self.n_estimators = n_estimators
        self.max_depth = max_depth
        self.learning_rate = learning_rate
        self.subsample = subsample
        self.random_state = random_state
        self.feature_engineering = feature_engineering
        self.lag_features = lag_features

        # 模型组件
        self.model = None
        self.scaler = StandardScaler()
        self.feature_columns = None
        self.target_scaler = StandardScaler()

        print(f"梯度提升模型初始化完成，使用CPU")
    
    def _create_lag_features(self, df: pd.DataFrame, target_col: str = 'load') -> pd.DataFrame:
        """创建滞后特征"""
        if not self.lag_features or target_col not in df.columns:
            return df
        
        df_with_lags = df.copy()
        
        # 添加滞后特征（1小时到1天）
        lag_periods = [1, 2, 4, 8, 12, 24, 48, 96]  # 15分钟到1天
        
        for lag in lag_periods:
            if lag < len(df):
                df_with_lags[f'{target_col}_lag_{lag}'] = df[target_col].shift(lag)
        
        # 添加滚动统计特征
        windows = [4, 8, 24, 96]  # 1小时到1天的窗口
        for window in windows:
            if window < len(df):
                df_with_lags[f'{target_col}_rolling_mean_{window}'] = df[target_col].rolling(window).mean()
                df_with_lags[f'{target_col}_rolling_std_{window}'] = df[target_col].rolling(window).std()
                df_with_lags[f'{target_col}_rolling_max_{window}'] = df[target_col].rolling(window).max()
                df_with_lags[f'{target_col}_rolling_min_{window}'] = df[target_col].rolling(window).min()
        
        # 添加差分特征
        df_with_lags[f'{target_col}_diff_1'] = df[target_col].diff(1)
        df_with_lags[f'{target_col}_diff_24'] = df[target_col].diff(24)  # 6小时差分
        df_with_lags[f'{target_col}_diff_96'] = df[target_col].diff(96)  # 1天差分
        
        print(f"滞后特征工程完成，特征数从 {df.shape[1]} 增加到 {df_with_lags.shape[1]}")
        
        return df_with_lags
    
    def _create_time_features(self, df: pd.DataFrame, datetime_col: str = 'dateTime') -> pd.DataFrame:
        """创建时间特征"""
        if not self.feature_engineering or datetime_col not in df.columns:
            return df
        
        df_with_time = df.copy()
        
        # 确保datetime列是datetime类型
        if not pd.api.types.is_datetime64_any_dtype(df_with_time[datetime_col]):
            df_with_time[datetime_col] = pd.to_datetime(df_with_time[datetime_col])
        
        # 基础时间特征
        df_with_time['hour'] = df_with_time[datetime_col].dt.hour
        df_with_time['minute'] = df_with_time[datetime_col].dt.minute
        df_with_time['dayofweek'] = df_with_time[datetime_col].dt.dayofweek
        df_with_time['day'] = df_with_time[datetime_col].dt.day
        df_with_time['month'] = df_with_time[datetime_col].dt.month
        df_with_time['quarter'] = df_with_time[datetime_col].dt.quarter
        
        # 周期性特征（正弦余弦编码）
        df_with_time['hour_sin'] = np.sin(2 * np.pi * df_with_time['hour'] / 24)
        df_with_time['hour_cos'] = np.cos(2 * np.pi * df_with_time['hour'] / 24)
        df_with_time['dayofweek_sin'] = np.sin(2 * np.pi * df_with_time['dayofweek'] / 7)
        df_with_time['dayofweek_cos'] = np.cos(2 * np.pi * df_with_time['dayofweek'] / 7)
        df_with_time['month_sin'] = np.sin(2 * np.pi * df_with_time['month'] / 12)
        df_with_time['month_cos'] = np.cos(2 * np.pi * df_with_time['month'] / 12)
        
        # 工作日/周末标识
        df_with_time['is_weekend'] = (df_with_time['dayofweek'] >= 5).astype(int)
        
        # 时段标识
        df_with_time['time_period'] = pd.cut(df_with_time['hour'],
                                           bins=[0, 6, 12, 18, 24],
                                           labels=[0, 1, 2, 3],
                                           include_lowest=True).astype(int)

        print(f"时间特征工程完成，新增 {df_with_time.shape[1] - df.shape[1]} 个时间特征")

        return df_with_time
    
    def _prepare_features(self, df: pd.DataFrame, target_col: str = 'load') -> tuple:
        """准备特征数据"""
        # 创建时间特征
        df_processed = self._create_time_features(df)
        
        # 如果包含目标列，创建滞后特征
        if target_col in df_processed.columns:
            df_processed = self._create_lag_features(df_processed, target_col)
        
        # 提取特征列（排除时间列和目标列）
        exclude_cols = ['dateTime', 'date', target_col]
        feature_cols = [col for col in df_processed.columns if col not in exclude_cols]
        
        # 填充缺失值
        df_processed[feature_cols] = df_processed[feature_cols].fillna(method='ffill').fillna(method='bfill').fillna(0)
        
        return df_processed, feature_cols
    
    def train(self, X_train: pd.DataFrame, y_train: pd.Series):
        """训练XGBoost模型"""
        print(f"开始训练XGBoost模型，训练样本数: {len(X_train)}")
        train_start = time.time()
        
        # 合并数据进行特征工程
        train_data = X_train.copy()
        train_data['load'] = y_train
        
        # 特征工程
        train_processed, feature_cols = self._prepare_features(train_data, 'load')
        self.feature_columns = feature_cols

        # 保存基础特征列表（不包含滞后特征）
        self.basic_features = [col for col in feature_cols
                              if not col.startswith('load_')]
        
        # 提取特征和目标（只使用基础特征，避免数据泄露）
        X_processed = train_processed[self.basic_features]
        y_processed = train_processed['load']
        
        # 移除包含NaN的行
        valid_mask = ~(X_processed.isna().any(axis=1) | y_processed.isna())
        X_processed = X_processed[valid_mask]
        y_processed = y_processed[valid_mask]
        
        print(f"特征工程后：{len(X_processed)} 个有效样本，{len(feature_cols)} 个特征")
        
        # 特征标准化
        X_scaled = self.scaler.fit_transform(X_processed)
        y_scaled = self.target_scaler.fit_transform(y_processed.values.reshape(-1, 1)).flatten()
        
        # 划分验证集
        val_size = int(0.2 * len(X_scaled))
        X_train_final = X_scaled[:-val_size]
        y_train_final = y_scaled[:-val_size]
        X_val = X_scaled[-val_size:]
        y_val = y_scaled[-val_size:]
        
        # 创建梯度提升模型
        self.model = GradientBoostingRegressor(
            n_estimators=self.n_estimators,
            max_depth=self.max_depth,
            learning_rate=self.learning_rate,
            subsample=self.subsample,
            random_state=self.random_state,
            verbose=1
        )

        # 训练模型
        self.model.fit(X_train_final, y_train_final)

        train_time = time.time() - train_start
        print(f"梯度提升模型训练完成，耗时: {train_time:.2f} 秒")
    
    def predict(self, X_test: pd.DataFrame) -> pd.DataFrame:
        """预测（原始简单版本）"""
        if self.model is None:
            raise ValueError("模型尚未训练，请先调用train()方法")

        print(f"开始梯度提升预测，样本数: {len(X_test)}")
        pred_start = time.time()

        # 创建时间特征
        test_processed = self._create_time_features(X_test)

        # 只选择基础特征（不包含滞后特征）
        X_test_processed = test_processed[self.basic_features]

        # 处理缺失值
        X_test_processed = X_test_processed.fillna(0)

        # 特征标准化
        X_scaled = self.scaler.transform(X_test_processed)

        # 预测
        predictions_scaled = self.model.predict(X_scaled)

        # 反标准化
        predictions = self.target_scaler.inverse_transform(predictions_scaled.reshape(-1, 1)).flatten()

        # 确保非负
        predictions = np.maximum(predictions, 0)

        pred_time = time.time() - pred_start
        print(f"梯度提升预测完成，耗时: {pred_time:.2f} 秒")

        return pd.DataFrame({'value': predictions})
    
    def get_feature_importance(self) -> pd.DataFrame:
        """获取特征重要性"""
        if self.model is None:
            raise ValueError("模型尚未训练")

        importance = self.model.feature_importances_
        importance_df = pd.DataFrame({
            'feature': self.feature_columns,
            'importance': importance
        }).sort_values('importance', ascending=False)

        return importance_df
    
    def save(self, path: str):
        """保存模型"""
        model_state = {
            'model': self.model,
            'scaler': self.scaler,
            'target_scaler': self.target_scaler,
            'feature_columns': self.feature_columns,
            'params': {
                'n_estimators': self.n_estimators,
                'max_depth': self.max_depth,
                'learning_rate': self.learning_rate,
                'subsample': self.subsample,
                'colsample_bytree': self.colsample_bytree,
                'random_state': self.random_state,
                'feature_engineering': self.feature_engineering,
                'lag_features': self.lag_features
            }
        }
        joblib.dump(model_state, path)
        print(f"梯度提升模型已保存至: {path}")

    def load(self, path: str):
        """加载模型"""
        model_state = joblib.load(path)

        self.model = model_state['model']
        self.scaler = model_state['scaler']
        self.target_scaler = model_state['target_scaler']
        self.feature_columns = model_state['feature_columns']

        # 恢复参数
        params = model_state['params']
        for key, value in params.items():
            setattr(self, key, value)

        print(f"梯度提升模型加载完成")
