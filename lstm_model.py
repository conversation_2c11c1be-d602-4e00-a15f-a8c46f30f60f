import numpy as np
import pandas as pd
import torch
import torch.nn as nn
from torch.utils.data import DataLoader, TensorDataset
from sklearn.preprocessing import MinMaxScaler
from sklearn.metrics import mean_squared_error, mean_absolute_error
import time
import joblib
from my_basic_model import MyBasicModel


class LSTMNet(nn.Module):
    """简单高效的LSTM神经网络模型"""
    def __init__(self, input_size, hidden_size=64, num_layers=2, dropout=0.2):
        super(LSTMNet, self).__init__()
        self.hidden_size = hidden_size
        self.num_layers = num_layers

        # LSTM层
        self.lstm = nn.LSTM(
            input_size=input_size,
            hidden_size=hidden_size,
            num_layers=num_layers,
            dropout=dropout if num_layers > 1 else 0,
            batch_first=True
        )

        # 全连接层
        self.fc = nn.Linear(hidden_size, 1)
        self.dropout = nn.Dropout(dropout)

    def forward(self, x):
        # LSTM前向传播
        lstm_out, _ = self.lstm(x)

        # 取最后一个时间步的输出
        last_output = lstm_out[:, -1, :]

        # Dropout和全连接层
        output = self.dropout(last_output)
        output = self.fc(output)

        return output


class LSTMModel(MyBasicModel):
    """LSTM时间序列预测模型"""
    
    def __init__(self, sequence_length=48, hidden_size=64, num_layers=2,
                 dropout=0.2, learning_rate=0.001, epochs=50, batch_size=32,
                 device='auto', early_stopping_patience=10):
        """
        初始化LSTM模型
        
        参数:
            sequence_length: 输入序列长度（时间步数）
            hidden_size: LSTM隐藏层大小
            num_layers: LSTM层数
            dropout: Dropout比率
            learning_rate: 学习率
            epochs: 训练轮数
            batch_size: 批次大小
            device: 计算设备 ('auto', 'cpu', 'cuda')
            early_stopping_patience: 早停耐心值
        """
        super().__init__()
        
        self.sequence_length = sequence_length
        self.hidden_size = hidden_size
        self.num_layers = num_layers
        self.dropout = dropout
        self.learning_rate = learning_rate
        self.epochs = epochs
        self.batch_size = batch_size
        self.early_stopping_patience = early_stopping_patience
        
        # 设备配置
        if device == 'auto':
            self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        else:
            self.device = torch.device(device)
        
        # 模型组件
        self.model = None
        self.scaler_X = MinMaxScaler()
        self.scaler_y = MinMaxScaler()
        self.feature_columns = None
        
        print(f"LSTM模型初始化完成，使用设备: {self.device}")
    
    def _prepare_sequences(self, X, y=None):
        """准备LSTM输入序列"""
        sequences = []
        targets = []
        
        for i in range(len(X) - self.sequence_length + 1):
            # 输入序列
            seq = X[i:i + self.sequence_length]
            sequences.append(seq)
            
            # 目标值（预测下一个时间点）
            if y is not None:
                target = y[i + self.sequence_length - 1]
                targets.append(target)
        
        sequences = np.array(sequences)
        if y is not None:
            targets = np.array(targets)
            return sequences, targets
        else:
            return sequences
    
    def train(self, X_train: pd.DataFrame, y_train: pd.Series):
        """训练LSTM模型"""
        print(f"开始训练LSTM模型，训练样本数: {len(X_train)}")
        train_start = time.time()
        
        # 保存特征列名
        self.feature_columns = X_train.columns.tolist()
        
        # 数据标准化
        X_scaled = self.scaler_X.fit_transform(X_train)
        y_scaled = self.scaler_y.fit_transform(y_train.values.reshape(-1, 1)).flatten()
        
        # 准备序列数据
        X_seq, y_seq = self._prepare_sequences(X_scaled, y_scaled)
        
        if len(X_seq) == 0:
            raise ValueError(f"数据长度不足以构建序列，需要至少{self.sequence_length}个样本")
        
        print(f"构建了 {len(X_seq)} 个训练序列，每个序列长度: {self.sequence_length}")
        
        # 转换为PyTorch张量
        X_tensor = torch.FloatTensor(X_seq).to(self.device)
        y_tensor = torch.FloatTensor(y_seq).to(self.device)
        
        # 创建数据加载器
        dataset = TensorDataset(X_tensor, y_tensor)
        dataloader = DataLoader(dataset, batch_size=self.batch_size, shuffle=True)
        
        # 初始化模型
        input_size = X_train.shape[1]
        self.model = LSTMNet(
            input_size=input_size,
            hidden_size=self.hidden_size,
            num_layers=self.num_layers,
            dropout=self.dropout
        ).to(self.device)
        
        # 损失函数和优化器
        criterion = nn.MSELoss()
        optimizer = torch.optim.Adam(self.model.parameters(), lr=self.learning_rate)
        scheduler = torch.optim.lr_scheduler.ReduceLROnPlateau(
            optimizer, mode='min', factor=0.5, patience=5
        )
        
        # 训练循环
        best_loss = float('inf')
        patience_counter = 0
        
        for epoch in range(self.epochs):
            self.model.train()
            total_loss = 0
            
            for batch_X, batch_y in dataloader:
                optimizer.zero_grad()
                
                # 前向传播
                outputs = self.model(batch_X).squeeze()
                loss = criterion(outputs, batch_y)
                
                # 反向传播
                loss.backward()
                optimizer.step()
                
                total_loss += loss.item()
            
            avg_loss = total_loss / len(dataloader)
            scheduler.step(avg_loss)
            
            # 早停检查
            if avg_loss < best_loss:
                best_loss = avg_loss
                patience_counter = 0
                # 保存最佳模型
                torch.save(self.model.state_dict(), 'best_lstm_model.pth')
            else:
                patience_counter += 1
            
            # 打印进度
            if (epoch + 1) % 20 == 0:
                print(f"Epoch [{epoch+1}/{self.epochs}], Loss: {avg_loss:.6f}")
            
            # 早停
            if patience_counter >= self.early_stopping_patience:
                print(f"早停触发，在第 {epoch+1} 轮停止训练")
                break
        
        # 加载最佳模型
        self.model.load_state_dict(torch.load('best_lstm_model.pth'))
        
        train_time = time.time() - train_start
        print(f"LSTM训练完成，耗时: {train_time:.2f} 秒")
    
    def predict(self, X_test: pd.DataFrame) -> pd.DataFrame:
        """预测"""
        if self.model is None:
            raise ValueError("模型尚未训练，请先调用train()方法")
        
        print(f"开始LSTM预测，样本数: {len(X_test)}")
        pred_start = time.time()
        
        # 确保特征顺序一致
        X_test = X_test[self.feature_columns]
        
        # 数据标准化
        X_scaled = self.scaler_X.transform(X_test)
        
        # 准备序列数据
        X_seq = self._prepare_sequences(X_scaled)
        
        if len(X_seq) == 0:
            raise ValueError(f"测试数据长度不足以构建序列，需要至少{self.sequence_length}个样本")
        
        # 转换为PyTorch张量
        X_tensor = torch.FloatTensor(X_seq).to(self.device)
        
        # 预测
        self.model.eval()
        predictions = []
        
        with torch.no_grad():
            for i in range(0, len(X_tensor), self.batch_size):
                batch = X_tensor[i:i + self.batch_size]
                outputs = self.model(batch)
                predictions.extend(outputs.cpu().numpy())
        
        # 反标准化
        predictions = np.array(predictions).reshape(-1, 1)
        predictions = self.scaler_y.inverse_transform(predictions).flatten()
        
        # 确保非负
        predictions = np.maximum(predictions, 0)
        
        pred_time = time.time() - pred_start
        print(f"LSTM预测完成，耗时: {pred_time:.2f} 秒")
        
        return pd.DataFrame({'value': predictions})
    
    def save(self, path: str):
        """保存模型"""
        model_state = {
            'model_state_dict': self.model.state_dict() if self.model else None,
            'scaler_X': self.scaler_X,
            'scaler_y': self.scaler_y,
            'feature_columns': self.feature_columns,
            'sequence_length': self.sequence_length,
            'hidden_size': self.hidden_size,
            'num_layers': self.num_layers,
            'dropout': self.dropout,
            'device': str(self.device)
        }
        joblib.dump(model_state, path)
        print(f"LSTM模型已保存至: {path}")
    
    def load(self, path: str):
        """加载模型"""
        model_state = joblib.load(path)
        
        self.scaler_X = model_state['scaler_X']
        self.scaler_y = model_state['scaler_y']
        self.feature_columns = model_state['feature_columns']
        self.sequence_length = model_state['sequence_length']
        self.hidden_size = model_state['hidden_size']
        self.num_layers = model_state['num_layers']
        self.dropout = model_state['dropout']
        
        # 重建模型
        if model_state['model_state_dict'] is not None:
            input_size = len(self.feature_columns)
            self.model = LSTMNet(
                input_size=input_size,
                hidden_size=self.hidden_size,
                num_layers=self.num_layers,
                dropout=self.dropout
            ).to(self.device)
            self.model.load_state_dict(model_state['model_state_dict'])
        
        print(f"LSTM模型加载完成")
