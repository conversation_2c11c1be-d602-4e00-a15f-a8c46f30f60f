# 配置文件 - 山西负荷预测项目

# 数据路径配置
data_path: "data.csv"

# 数据处理方式选择
use_sliding_window: false  # true=滑动窗口(适合TabPFN), false=普通时间序列(适合LSTM)
use_enhanced_features: false  # 先用基础特征测试，避免过于复杂

# 模型配置
use_tabpfn: false  # 禁用TabPFN
tabpfn_params:
  device: "auto"  # 自动选择设备 (auto/cpu/cuda)
  random_state: 80
  use_aggregation: false  # 关键：禁用聚合，直接使用原始特征

use_lstm: true  # 启用LSTM模型
lstm_params:
  sequence_length: 48  # 最佳效果时使用的序列长度
  hidden_size: 64     # LSTM隐藏层大小
  num_layers: 2       # LSTM层数
  dropout: 0.2        # Dropout比率
  learning_rate: 0.001 # 学习率
  epochs: 50          # 训练轮数
  batch_size: 32      # 批次大小
  device: "auto"      # 计算设备
  early_stopping_patience: 10  # 早停耐心值

use_xgboost: false  # 暂时禁用XGBoost，测试TabPFN
xgboost_params:
  n_estimators: 500      # 树的数量
  max_depth: 6           # 树的最大深度
  learning_rate: 0.1     # 学习率
  subsample: 0.8         # 样本采样比例
  random_state: 42       # 随机种子
  feature_engineering: true  # 是否进行特征工程
  lag_features: false    # 禁用滞后特征（避免数据泄露）

# 数据划分配置
test_size: 0.1  # 测试集占比 (2% - 最佳效果时的设置)

# 评估配置
metric_to_compare: "rmse"  # 用于比较模型的指标 (rmse/mae)

# 预测配置
save_predictions: true
plot_predictions: true
future_days: 7  # 预测未来天数

# 输出配置
output_dir: "results"
