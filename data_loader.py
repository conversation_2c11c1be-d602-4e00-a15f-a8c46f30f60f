# import pandas as pd
# import numpy as np
# import time
# from datetime import datetime, timedelta
# from typing import Optional


# class DataPreprocessor:
#     """
#     数据预处理类，自动同步负荷数据和天气数据的频率
#     """
#     def __init__(
#         self,
#         start_date: str,
#         end_date: str,
#         excel_file_path: str = '山西零售用户负荷.xlsx',
#         weather_csv_path: str = "weather_山西.csv"
#     ):
#         """
#         初始化数据预处理类
        
#         参数:
#             start_date: 统一的开始日期 (yyyy-MM-dd)
#             end_date: 统一的结束日期 (yyyy-MM-dd)
#             excel_file_path: 负荷数据Excel文件路径
#             weather_csv_path: 天气数据CSV文件路径
#         """
#         self.start_date = start_date
#         self.end_date = end_date
#         self.excel_file_path = excel_file_path
#         self.weather_csv_path = weather_csv_path
#         self.load_freq: Optional[str] = None 

        
#         self.X_train = None  # 训练集特征
#         self.y_train = None  # 训练集标签
#         self.X_test = None  # 测试集特征
#         self.y_test = None  # 测试集标签  
        
#         # 验证日期格式
#         try:
#             datetime.strptime(start_date, '%Y-%m-%d')
#             datetime.strptime(end_date, '%Y-%m-%d')
#         except ValueError:
#             raise ValueError("日期格式错误，需使用'yyyy-MM-dd'格式")

#     # ----------------------- 加载负荷 ----------------------- #
#     def load_user_load(self, customer_code: str) -> pd.DataFrame:
#         """
#         从Excel文件加载单个客户的负荷数据，并自动识别数据频率
#         """
#         # 校验必需参数
#         if not customer_code:
#             raise ValueError("必须指定customer_code参数")
        
#         # 读取Excel文件
#         try:
#             xls = pd.ExcelFile(self.excel_file_path)
#         except FileNotFoundError:
#             raise FileNotFoundError(f"负荷数据文件不存在：{self.excel_file_path}")
        
#         # 检查客户sheet是否存在
#         if customer_code not in xls.sheet_names:
#             raise ValueError(f"客户代码 '{customer_code}' 不存在于Excel文件中")
        
#         print(f"读取客户 '{customer_code}' 的负荷数据...")
#         df = xls.parse(customer_code)
        
#         # 解析日期和时间（行列转换）
#         time_cols = df.columns[1:]  # 时间点列（从第二列开始）
#         timestamps, loads = [], []
        
#         for _, date_row in df.iterrows():
#             date_str = date_row.iloc[0]  # 日期值（第一列）
#             for time_col in time_cols:
#                 time_str = time_col
#                 load_value = date_row[time_col]
#                 try:
#                     # 组合日期和时间为时间戳
#                     timestamp = pd.to_datetime(f"{date_str} {time_str}")
#                     timestamps.append(timestamp)
#                     loads.append(load_value)
#                 except:
#                     continue  # 跳过解析失败的单元格
        
#         # 构造负荷数据DataFrame
#         load_df = pd.DataFrame({'dateTime': timestamps, 'load': loads})
        
#         # 使用初始化的日期范围筛选
#         load_df = load_df[
#             (load_df['dateTime'] >= pd.to_datetime(self.start_date)) &
#             (load_df['dateTime'] < pd.to_datetime(self.end_date))
#         ]
        
#         # 排序并重置索引
#         load_df = load_df.sort_values('dateTime').reset_index(drop=True)
        
#         # 自动识别负荷数据的频率
#         if len(load_df) >= 2:
#             # 计算前两个时间点的差值，作为频率
#             time_delta = load_df['dateTime'].iloc[1] - load_df['dateTime'].iloc[0]
#             self.load_freq = self._timedelta_to_freq(time_delta)
#             print(f"识别到负荷数据频率：{self.load_freq}")
#         else:
#             raise ValueError("负荷数据点数不足（至少需要2条），无法识别频率")
        
#         print(f"负荷数据处理完成，共 {len(load_df)} 条记录")
#         return load_df

#     # ----------------------- 加载天气 ----------------------- #
#     def load_weather_data(self) -> pd.DataFrame:
#         """
#         从CSV文件加载天气数据（使用负荷数据的频率）
#         注意：此方法**不执行特征工程**，仅处理频率对齐和插值
#         """
#         # 确保已经加载过负荷数据并识别了频率
#         if self.load_freq is None:
#             raise ValueError("请先调用load_user_load方法识别负荷数据频率")
        
#         # 读取天气数据
#         try:
#             df = pd.read_csv(self.weather_csv_path)
#         except FileNotFoundError:
#             raise FileNotFoundError(f"天气数据文件不存在：{self.weather_csv_path}")
        
#         # 转换时间列并过滤无效值
#         df['t_datetime_cst'] = pd.to_datetime(df['t_datetime_cst'], errors='coerce')
#         df = df.dropna(subset=['t_datetime_cst']).copy()
        
#         # 使用初始化的日期范围筛选
#         start_date_obj = datetime.strptime(self.start_date, '%Y-%m-%d')
#         end_date_obj = datetime.strptime(self.end_date, '%Y-%m-%d')
#         df = df[
#             (df['t_datetime_cst'] >= start_date_obj) & 
#             (df['t_datetime_cst'] < end_date_obj)
#         ]
        
#         print(f"天气数据筛选完成，共 {len(df)} 条原始记录")
        
#         # 构造完整时间序列（使用负荷数据的频率）
#         full_range = pd.date_range(
#             start=start_date_obj,
#             end=end_date_obj - timedelta(seconds=1),
#             freq=self.load_freq
#         )
#         time_df = pd.DataFrame({"t_datetime_cst": full_range})
        
#         # 合并数据并插值填补缺失值
#         weather_df = pd.merge(time_df, df, on="t_datetime_cst", how="left")
#         numeric_cols = weather_df.select_dtypes(include=np.number).columns.tolist()
        
#         # 时间插值（确保频率一致）
#         weather_df = weather_df.set_index('t_datetime_cst')
#         weather_df[numeric_cols] = weather_df[numeric_cols].interpolate(method='time').bfill()
#         weather_df = weather_df.reset_index()
        
#         print(f"天气数据处理完成，按负荷频率 '{self.load_freq}' 生成 {len(weather_df)} 条记录")
        
#         # 返回未做特征工程的原始天气数据
#         return weather_df

#     # ----------------------- 数据合并 ----------------------- #
#     def merge_load_and_weather(self, customer_code: str) -> pd.DataFrame:
#         """
#         合并负荷数据和天气数据（自动同步频率）
#         注意：此方法**不执行特征工程**，仅合并数据
#         """
#         load_data = self.load_user_load(customer_code)
#         weather_data = self.load_weather_data()  # 加载未做特征工程的天气数据
#         weather_data = weather_data.rename(columns={'t_datetime_cst': 'dateTime'}) # 统一时间列名为 'dateTime'
        
#         # 按时间列合并数据（移除重复的时间列）
#         merged_data = pd.merge(
#             load_data,
#             weather_data,
#             on = "dateTime",
#             how = "inner"
#         )
        
#         # 清理冗余列（可选，根据需求保留）
#         # 如需更简洁，可删除非必要列（如原始天气数据的无关标识列）
#         # merged_data = merged_data.drop(columns=['冗余列1', '冗余列2'])
        
#         print(f"数据合并完成，共 {len(merged_data)} 条记录")
#         return merged_data

#     def _timedelta_to_freq(self, delta: pd.Timedelta) -> str:
#         """将时间差转换为pandas频率字符串"""
#         seconds = delta.total_seconds()
#         if seconds % 86400 == 0:
#             return f"{int(seconds // 86400)}D"  # 天
#         elif seconds % 3600 == 0:
#             return f"{int(seconds // 3600)}H"  # 小时
#         elif seconds % 60 == 0:
#             return f"{int(seconds // 60)}T"  # 分钟（pandas中T代表分钟）
#         else:
#             return f"{seconds}S"  # 秒

#     # ----------------------- 特征工程 ----------------------- #
#     def process_weather_features(self, df: pd.DataFrame) -> pd.DataFrame:
#         """
#         对天气数据进行特征工程处理（新增时间、节假日等特征）
#         此方法可在外部手动调用，按需执行特征工程
#         """
#         feature_start_time = time.time()
#         print(f"\n🔧 开始天气数据特征工程...")
        
#         # 1. 重命名时间列为统一的'dateTime'
#         df = df.rename(columns={'t_datetime_cst': 'dateTime'})
        
#         # 2. 添加日期列（用于筛选和分组）
#         df['date'] = df['dateTime'].dt.date.astype(str)
        
#         # 3. 添加节假日特征
#         df = self._with_holiday_features(df, self.start_date, self.end_date)
        
#         # 4. 时间特征（适配15分钟数据）
#         df['hour'] = df['dateTime'].dt.hour  # 小时（0-23）
#         df['minute'] = df['dateTime'].dt.minute  # 分钟（0-59）
#         df['time_of_day'] = df['hour'] * 60 + df['minute']  # 一天中的分钟数（0-1439）
        
#         # 5. 周特征
#         df['dayofweek'] = df['dateTime'].dt.dayofweek  # 星期几（0=周一，6=周日）
#         df['is_weekend'] = (df['dayofweek'] >= 5).astype(int)  # 周末标识（1=周末，0=工作日）
        
#         # 6. 月内周特征
#         df['week_of_month'] = self.week_of_month(df['dateTime'])
        
#         # 7. 天数索引（增强临近日期的关联性）
#         df['day_index'] = (df.groupby(df['dateTime'].dt.date).ngroup() + 1)
        
#         # 计算特征工程耗时
#         feature_time = time.time() - feature_start_time
#         print(f"   ✅ 天气特征工程完成，新增 {len(df.columns) - len(df.columns.drop(['hour', 'minute', 'is_weekend', 'week_of_month', 'day_index', 'holiday', 'day_type']))} 个特征")
#         print(f"   ⏱️  特征工程耗时: {feature_time:.2f} 秒")
        
#         return df

#     # ----------------------- 节假日 ----------------------- #
#     def _with_holiday_features(self, df: pd.DataFrame, start_day: str, end_day: str) -> pd.DataFrame:
#         """添加节假日和工作日类型特征"""
#         # 生成日期范围（包含start_day到end_day的所有日期）
#         date_range = pd.date_range(start=start_day, end=end_day)
#         date_df = pd.DataFrame({'date': date_range.strftime("%Y-%m-%d")})
        
#         # 简单模拟节假日（实际应用中可替换为真实节假日数据）
#         # 这里用星期几模拟：周六日为节假日，其余为工作日
#         date_df['dayofweek'] = pd.to_datetime(date_df['date']).dt.dayofweek
#         date_df['day_type'] = date_df['dayofweek'].apply(
#             lambda x: 6 if x == 5 else 7 if x == 6 else 1  # 6=周六，7=周日，1=工作日
#         )
#         date_df['holiday'] = date_df['day_type'].apply(lambda x: 1 if x in [6, 7] else 0)  # 1=节假日，0=工作日
        
#         # 合并到原始数据
#         df = pd.merge(df, date_df, on='date', how='left')
#         return df

#     # ----------------------- 月内周----------------------- #
#     def week_of_month(self, date_series: pd.Series) -> pd.Series:
#         """计算日期在当月的第几周（从1开始）"""
#         # 确保输入为datetime类型
#         if not pd.api.types.is_datetime64_any_dtype(date_series):
#             date_series = pd.to_datetime(date_series)
        
#         # 当月第一天
#         first_day = date_series.dt.to_period('M').dt.start_time
#         # 当月第一周的周一
#         first_week_monday = first_day - pd.to_timedelta(first_day.dt.dayofweek, unit='d')
#         # 计算当前日期所在周与第一周的差值（以周为单位）
#         week_num = ((date_series - first_week_monday) // pd.Timedelta(weeks=1)).astype(int) + 1
#         return week_num

    
#     # ----------------------- 划分测试集 ----------------------- #
#     def split_train_test_by_ratio(self, merged_df: pd.DataFrame, test_ratio: float = 0.2) -> tuple:
#         """
#         按比例划分训练集和测试集（时间序列专用，保留时间顺序）
        
#         参数:
#             merged_df: 合并后的数据集（需包含'dateTime'和'load'列）
#             test_ratio: 测试集占比（默认0.2，即20%；范围：0.05-0.5）
        
#         返回:
#             (X_train, X_test, y_train, y_test): 划分后的特征和标签
#         """
#         # 校验必要列
#         if 'dateTime' not in merged_df.columns or 'load' not in merged_df.columns:
#             raise ValueError("数据集必须包含'dateTime'和'load'列")
        
#         # 按时间排序
#         merged_df = merged_df.sort_values('dateTime').reset_index(drop=True)
#         total_samples = len(merged_df)
        
#         # 计算测试集大小（向上取整，确保至少1条）
#         test_size = max(1, round(total_samples * test_ratio))
#         train_size = total_samples - test_size
        
#         # 划分（后test_ratio比例作为测试集）
#         train_data = merged_df.iloc[:train_size]
#         test_data = merged_df.iloc[train_size:]
        
#         # 分离特征和标签
#         X_train = train_data.drop(columns=['load', 'dateTime'])
#         y_train = train_data['load']
#         X_test = test_data.drop(columns=['load', 'dateTime'])
#         y_test = test_data['load']
        
#         # 打印划分结果
#         actual_ratio = len(test_data) / total_samples * 100
#         print(f"按{test_ratio*100}%比例划分完成：")
#         print(f"训练集：{len(train_data)}条，测试集：{len(test_data)}条，实际测试集占比：{actual_ratio:.1f}%")
        
#         self.X_train = X_train  # 保存训练集特征
#         self.y_train = y_train  # 保存训练集标签
#         self.X_test = X_test    # 保存测试集特征
#         self.y_test = y_test    # 保存测试集标签

#         return X_train, X_test, y_train, y_test


#     # # ----------------------- 新增：获取训练集和测试集 ----------------------- #
#     # def get_train_data(self) -> tuple:
#     #     """获取划分后的训练集特征和标签"""
#     #     if self.X_train is None or self.y_train is None:
#     #         raise ValueError("训练集未划分，请先调用 split_train_test_by_ratio() 方法")
#     #     return self.X_train, self.y_train

#     # def get_test_data(self) -> tuple:
#     #     """获取划分后的测试集特征和标签"""
#     #     if self.X_test is None or self.y_test is None:
#     #         raise ValueError("测试集未划分，请先调用 split_train_test_by_ratio() 方法")
#     #     return self.X_test, self.y_test

import pandas as pd
import numpy as np
import time
from datetime import datetime, timedelta
from typing import Optional, Tuple, List


class DataPreprocessor:
    """
    数据预处理类，自动同步负荷数据和天气数据的频率
    """
    def __init__(
        self,
        start_date: str,
        end_date: str,
        excel_file_path: str = '山西零售用户负荷.xlsx',
        weather_csv_path: str = "weather_山西.csv",
        history_days: int = 7  # 历史窗口大小（优化为7天）
    ):
        """
        初始化数据预处理类
        
        参数:
            start_date: 统一的开始日期 (yyyy-MM-dd)
            end_date: 统一的结束日期 (yyyy-MM-dd)
            excel_file_path: 负荷数据Excel文件路径
            weather_csv_path: 天气数据CSV文件路径
            history_days: 历史窗口大小（用于滑动窗口数据生成）
        """
        self.start_date = start_date
        self.end_date = end_date
        self.excel_file_path = excel_file_path
        self.weather_csv_path = weather_csv_path
        self.history_days = history_days
        self.load_freq: Optional[str] = None 

        
        self.X_train = None  # 训练集特征
        self.y_train = None  # 训练集标签
        self.X_test = None  # 测试集特征
        self.y_test = None  # 测试集标签  
        
        # 验证日期格式
        try:
            datetime.strptime(start_date, '%Y-%m-%d')
            datetime.strptime(end_date, '%Y-%m-%d')
        except ValueError:
            raise ValueError("日期格式错误，需使用'yyyy-MM-dd'格式")

    # ----------------------- 加载负荷 ----------------------- #
    def load_user_load(self, customer_code: str) -> pd.DataFrame:
        """从Excel文件加载单个客户的负荷数据，并自动识别数据频率"""
        # 校验必需参数
        if not customer_code:
            raise ValueError("必须指定customer_code参数")
        
        # 读取Excel文件
        try:
            xls = pd.ExcelFile(self.excel_file_path)
        except FileNotFoundError:
            raise FileNotFoundError(f"负荷数据文件不存在：{self.excel_file_path}")
        
        # 检查客户sheet是否存在
        if customer_code not in xls.sheet_names:
            raise ValueError(f"客户代码 '{customer_code}' 不存在于Excel文件中")
        
        print(f"读取客户 '{customer_code}' 的负荷数据...")
        df = xls.parse(customer_code)
        
        # 解析日期和时间（行列转换）
        time_cols = df.columns[1:]  # 时间点列（从第二列开始）
        timestamps, loads = [], []
        
        for _, date_row in df.iterrows():
            date_str = date_row.iloc[0]  # 日期值（第一列）
            for time_col in time_cols:
                time_str = time_col
                load_value = date_row[time_col]
                try:
                    # 组合日期和时间为时间戳
                    timestamp = pd.to_datetime(f"{date_str} {time_str}")
                    timestamps.append(timestamp)
                    loads.append(load_value)
                except:
                    continue  # 跳过解析失败的单元格
        
        # 构造负荷数据DataFrame
        load_df = pd.DataFrame({'dateTime': timestamps, 'load': loads})
        
        # 使用初始化的日期范围筛选
        load_df = load_df[
            (load_df['dateTime'] >= pd.to_datetime(self.start_date)) &
            (load_df['dateTime'] < pd.to_datetime(self.end_date))
        ]
        
        # 排序并重置索引
        load_df = load_df.sort_values('dateTime').reset_index(drop=True)
        
        # 自动识别负荷数据的频率
        if len(load_df) >= 2:
            # 计算前两个时间点的差值，作为频率
            time_delta = load_df['dateTime'].iloc[1] - load_df['dateTime'].iloc[0]
            self.load_freq = self._timedelta_to_freq(time_delta)
            print(f"识别到负荷数据频率：{self.load_freq}")
        else:
            raise ValueError("负荷数据点数不足（至少需要2条），无法识别频率")
        
        print(f"负荷数据处理完成，共 {len(load_df)} 条记录")
        return load_df

    # ----------------------- 加载天气 ----------------------- #
    def load_weather_data(self) -> pd.DataFrame:
        """
        从CSV文件加载天气数据（使用负荷数据的频率）
        注意：此方法**不执行特征工程**，仅处理频率对齐和插值
        """
        # 确保已经加载过负荷数据并识别了频率
        if self.load_freq is None:
            raise ValueError("请先调用load_user_load方法识别负荷数据频率")
        
        # 读取天气数据
        try:
            df = pd.read_csv(self.weather_csv_path)
            # 选择核心天气特征（10个），配合时间特征达到18个总特征
            weather_cols = ['t_datetime_cst', 't_2m', 'rh_2m', 'tmax_2m', 'tmin_2m',
                           'u_10m', 'v_10m', 'sp_surface', 'tp_surface', 'dswrf_surface']
            df = df[weather_cols]
            print(f"选择了 {len(weather_cols)-1} 个核心天气特征")
        except FileNotFoundError:
            raise FileNotFoundError(f"天气数据文件不存在：{self.weather_csv_path}")
        
        # 转换时间列并过滤无效值
        df['t_datetime_cst'] = pd.to_datetime(df['t_datetime_cst'], errors='coerce')
        df = df.dropna(subset=['t_datetime_cst']).copy()
        
        # 使用初始化的日期范围筛选
        start_date_obj = datetime.strptime(self.start_date, '%Y-%m-%d')
        end_date_obj = datetime.strptime(self.end_date, '%Y-%m-%d')
        df = df[
            (df['t_datetime_cst'] >= start_date_obj) & 
            (df['t_datetime_cst'] < end_date_obj)
        ]
        
        print(f"天气数据筛选完成，共 {len(df)} 条原始记录")
        
        # 构造完整时间序列（使用负荷数据的频率）
        full_range = pd.date_range(
            start=start_date_obj,
            end=end_date_obj - timedelta(seconds=1),
            freq=self.load_freq
        )
        time_df = pd.DataFrame({"t_datetime_cst": full_range})
        
        # 合并数据并插值填补缺失值
        weather_df = pd.merge(time_df, df, on="t_datetime_cst", how="left")
        numeric_cols = weather_df.select_dtypes(include=np.number).columns.tolist()
        
        # 时间插值（确保频率一致）
        weather_df = weather_df.set_index('t_datetime_cst')
        weather_df[numeric_cols] = weather_df[numeric_cols].interpolate(method='time').bfill()
        weather_df = weather_df.reset_index()
        
        print(f"天气数据处理完成，按负荷频率 '{self.load_freq}' 生成 {len(weather_df)} 条记录")
        
        # 返回未做特征工程的原始天气数据
        return weather_df

    # ----------------------- 数据合并 ----------------------- #
    def merge_load_and_weather(self, customer_code: str) -> pd.DataFrame:
        """
        合并负荷数据和天气数据（自动同步频率）
        注意：此方法**不执行特征工程**，仅合并数据
        """
        load_data = self.load_user_load(customer_code)
        weather_data = self.load_weather_data()  # 加载未做特征工程的天气数据
        weather_data = weather_data.rename(columns={'t_datetime_cst': 'dateTime'}) # 统一时间列名为 'dateTime'
        
        # 按时间列合并数据（移除重复的时间列）
        merged_data = pd.merge(
            load_data,
            weather_data,
            on = "dateTime",
            how = "inner"
        )
        
        print(f"数据合并完成，共 {len(merged_data)} 条记录")
        return merged_data

    def _timedelta_to_freq(self, delta: pd.Timedelta) -> str:
        """将时间差转换为pandas频率字符串"""
        seconds = delta.total_seconds()
        if seconds % 86400 == 0:
            return f"{int(seconds // 86400)}D"  # 天
        elif seconds % 3600 == 0:
            return f"{int(seconds // 3600)}H"  # 小时
        elif seconds % 60 == 0:
            return f"{int(seconds // 60)}T"  # 分钟（pandas中T代表分钟）
        else:
            return f"{seconds}S"  # 秒

    # # ----------------------- 特征工程（基础班） ----------------------- #
    # def process_weather_features(self, df: pd.DataFrame) -> pd.DataFrame:
    #     """对天气数据进行特征工程处理（新增时间、节假日等特征）"""
    #     feature_start_time = time.time()
    #     print(f"\n🔧 开始天气数据特征工程...")
        
    #     # 1. 重命名时间列为统一的'dateTime'
    #     df = df.rename(columns={'t_datetime_cst': 'dateTime'})
        
    #     # 2. 添加日期列（用于筛选和分组）
    #     df['date'] = df['dateTime'].dt.date.astype(str)
        
    #     # 3. 添加节假日特征
    #     df = self._with_holiday_features(df, self.start_date, self.end_date)
        
    #     # 4. 时间特征（适配15分钟数据）
    #     df['hour'] = df['dateTime'].dt.hour  # 小时（0-23）
    #     df['minute'] = df['dateTime'].dt.minute  # 分钟（0-59）
    #     df['time_of_day'] = df['hour'] * 60 + df['minute']  # 一天中的分钟数（0-1439）
        
    #     # 5. 周特征
    #     df['dayofweek'] = df['dateTime'].dt.dayofweek  # 星期几（0=周一，6=周日）
    #     df['is_weekend'] = (df['dayofweek'] >= 5).astype(int)  # 周末标识（1=周末，0=工作日）
        
    #     # 6. 月内周特征
    #     df['week_of_month'] = self.week_of_month(df['dateTime'])
        
    #     # 7. 天数索引（增强临近日期的关联性）
    #     df['day_index'] = (df.groupby(df['dateTime'].dt.date).ngroup() + 1)
        
    #     # 计算特征工程耗时
    #     feature_time = time.time() - feature_start_time
    #     print(f"   ✅ 天气特征工程完成，新增 {len(df.columns) - len(df.columns.drop(['hour', 'minute', 'is_weekend', 'week_of_month', 'day_index', 'holiday', 'day_type']))} 个特征")
    #     print(f"   ⏱️  特征工程耗时: {feature_time:.2f} 秒")
        
    #     return df

    # # ----------------------- 基础时间特征（恢复18个特征用） ----------------------- #
    # def add_basic_time_features(self, df: pd.DataFrame) -> pd.DataFrame:
    #     """添加基础时间特征，恢复到18个特征以获得9.23% MAPE效果"""
    #     df = df.copy()

    #     # 重命名时间列
    #     df = df.rename(columns={'t_datetime_cst': 'dateTime'})

    #     # 添加基础时间特征（8个）
    #     df['hour'] = df['dateTime'].dt.hour  # 小时（0-23）
    #     df['dayofweek'] = df['dateTime'].dt.dayofweek  # 星期几（0-6）
    #     df['month'] = df['dateTime'].dt.month  # 月份（1-12）
    #     df['is_weekend'] = (df['dayofweek'] >= 5).astype(int)  # 周末标识

    #     # 添加周期性编码（正弦余弦）
    #     df['hour_sin'] = np.sin(2 * np.pi * df['hour'] / 24)
    #     df['hour_cos'] = np.cos(2 * np.pi * df['hour'] / 24)
    #     df['dayofweek_sin'] = np.sin(2 * np.pi * df['dayofweek'] / 7)
    #     df['dayofweek_cos'] = np.cos(2 * np.pi * df['dayofweek'] / 7)

    #     # 计算特征数（排除时间列）
    #     feature_count = len([col for col in df.columns if col != 'dateTime'])
    #     print(f"添加基础时间特征完成，总特征数: {feature_count}个")

    #     return df

    # # ----------------------- 特征工程 ----------------------- #
    # def process_weather_features(self, df: pd.DataFrame) -> pd.DataFrame:
    #     """对天气数据进行特征工程处理（新增时间、节假日、统计特征等）"""
    #     feature_start_time = time.time()
    #     print(f"\n🔧 开始天气数据特征工程...")
        
    #     # 基础时间特征处理
    #     # 确保时间列名为'dateTime'（合并后的数据已处理，此处兼容）
    #     if 't_datetime_cst' in df.columns:
    #         df = df.rename(columns={'t_datetime_cst': 'dateTime'})
        
    #     # 添加日期列（用于筛选和分组）
    #     df['date'] = df['dateTime'].dt.date.astype(str)
        
    #     # 添加节假日特征
    #     df = self._with_holiday_features(df, self.start_date, self.end_date)
        
    #     # 时间特征（适配15分钟数据）
    #     df['hour'] = df['dateTime'].dt.hour  # 小时（0-23）
    #     df['minute'] = df['dateTime'].dt.minute  # 分钟（0-59）
    #     df['time_of_day'] = df['hour'] * 60 + df['minute']  # 一天中的分钟数（0-1439）
        
    #     # 周特征
    #     df['dayofweek'] = df['dateTime'].dt.dayofweek  # 星期几（0=周一，6=周日）
    #     df['is_weekend'] = (df['dayofweek'] >= 5).astype(int)  # 周末标识（1=周末，0=工作日）
        
    #     # 月内周特征
    #     df['week_of_month'] = self.week_of_month(df['dateTime'])
        
    #     # 天数索引（增强临近日期的关联性）
    #     df['day_index'] = (df.groupby(df['dateTime'].dt.date).ngroup() + 1)
        
    #     # 添加周期性特征
    #     df['sin_time_of_day'] = np.sin(2 * np.pi * df['time_of_day'] / 1440)
    #     df['cos_time_of_day'] = np.cos(2 * np.pi * df['time_of_day'] / 1440)
    #     df['sin_day_of_week'] = np.sin(2 * np.pi * df['dayofweek'] / 7)
    #     df['cos_day_of_week'] = np.cos(2 * np.pi * df['dayofweek'] / 7)
        
    #     # 添加单变量统计特征（使用实际天气列名）
    #     # 天气列说明：t_2m(温度)、rh_2m(湿度)、u_10m(东向风速)、v_10m(北向风速)、sp_surface(气压)
    #     weather_cols = ['t_2m', 'rh_2m', 'u_10m', 'v_10m', 'sp_surface']
    #     # 窗口大小：基于数据频率（假设15分钟频率，4=1小时，24=6小时，96=1天）
    #     windows = [4, 24, 96]
    #     df = self.add_univariate_stats(df, weather_cols, windows)
        
    #     # 添加二元统计特征（暂时禁用）
    #     # col_pairs = [
    #     #     ('t_2m', 'rh_2m'),  # 温度与湿度
    #     #     ('t_2m', 'sp_surface'),  # 温度与气压
    #     #     ('u_10m', 'v_10m'),  # 东向与北向风速（可反映风速大小）
    #     #     ('t_2m', 'dswrf_surface')  # 温度与太阳辐射
    #     # ]
    #     # df = self.add_bivariate_stats(df, col_pairs, windows)
        
    #     # 计算特征工程耗时
    #     feature_time = time.time() - feature_start_time

    #     # 统计新增特征数（只排除实际存在的列）
    #     exclude_cols = ['hour', 'minute', 'time_of_day', 'dayofweek', 'is_weekend',
    #                    'week_of_month', 'day_index', 'holiday', 'day_type',
    #                    'sin_time_of_day', 'cos_time_of_day', 'sin_day_of_week',
    #                    'cos_day_of_week']

    #     # 添加滚动统计特征列名
    #     rolling_cols = [f'{col}_rolling_{stat}_{w}' for col in weather_cols
    #                    for stat in ['mean', 'std', 'min', 'max', 'skew'] for w in windows]
    #     exclude_cols.extend(rolling_cols)

    #     # 只排除实际存在的列
    #     existing_exclude_cols = [col for col in exclude_cols if col in df.columns]
    #     original_cols = df.columns.drop(existing_exclude_cols)

    #     new_feature_count = len(df.columns) - len(original_cols)
    #     print(f"   ✅ 天气特征工程完成，新增 {new_feature_count} 个特征")
    #     print(f"   ⏱️  特征工程耗时: {feature_time:.2f} 秒")
        
    #     return df

    # def add_univariate_stats(self, df: pd.DataFrame, cols: list, windows: list) -> pd.DataFrame:
    #     """添加单变量统计特征（类实例方法）"""
    #     for col in cols:
    #         for window in windows:
    #             df[f'{col}_rolling_mean_{window}'] = df[col].rolling(window=window).mean()
    #             df[f'{col}_rolling_std_{window}'] = df[col].rolling(window=window).std()
    #             df[f'{col}_rolling_min_{window}'] = df[col].rolling(window=window).min()
    #             df[f'{col}_rolling_max_{window}'] = df[col].rolling(window=window).max()
    #             df[f'{col}_rolling_skew_{window}'] = df[col].rolling(window=window).skew()
    #     return df

    # # def add_bivariate_stats(self, df: pd.DataFrame, col_pairs: list, windows: list) -> pd.DataFrame:
    # #     """添加二元统计特征（类实例方法）"""
    # #     for col1, col2 in col_pairs:
    # #         for window in windows:
    # #             # 相关性（处理窗口内数据不足的情况）
    # #             df[f'{col1}_{col2}_corr_{window}'] = df[col1].rolling(window=window).corr(df[col2])
    # #             # 协方差
    # #             df[f'{col1}_{col2}_cov_{window}'] = df[col1].rolling(window=window).cov(df[col2])
    # #             # 标准化差异（避免除零）
    # #             with np.errstate(divide='ignore', invalid='ignore'):
    # #                 df[f'{col1}_{col2}_zscore_{window}'] = (df[col1] - df[col2]) / df[col2].rolling(window=window).std()
    # #             # 填充标准化差异中的NaN/inf
    # #             df[f'{col1}_{col2}_zscore_{window}'] = df[f'{col1}_{col2}_zscore_{window}'].fillna(0)
    # #     return df

    # ----------------------- 特征工程 ----------------------- #
    def process_weather_features(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        对天气数据进行特征工程处理（新增时间、节假日等特征）
        此方法可在外部手动调用，按需执行特征工程
        """
        feature_start_time = time.time()
        print(f"\n🔧 开始天气数据特征工程...")
        
        # 重命名时间列为统一的'dateTime'
        df = df.rename(columns={'t_datetime_cst': 'dateTime'})
        
        # 1. 添加日期列（用于筛选和分组）
        df['date'] = df['dateTime'].dt.date.astype(str)
        
        # 2. 添加节假日特征
        df = self._with_holiday_features(df, self.start_date, self.end_date)
        
        # 时间特征（适配15分钟数据）
        # 3. 添加小时特征
        df['hour'] = df['dateTime'].dt.hour  # 小时（0-23）

        # 4. 添加分钟特征
        df['minute'] = df['dateTime'].dt.minute  # 分钟（0-59）

        # 5. 添加一天中的分钟数特征
        df['time_of_day'] = df['hour'] * 60 + df['minute']  # 一天中的分钟数（0-1439）
        
        # 周特征
        # 6. 添加周几特征
        df['dayofweek'] = df['dateTime'].dt.dayofweek  # 星期几（0=周一，6=周日）

        # 7. 添加周末特征
        df['is_weekend'] = (df['dayofweek'] >= 5).astype(int)  # 周末标识（1=周末，0=工作日）
        
        # 8. 月内周特征
        df['week_of_month'] = self.week_of_month(df['dateTime'])
        
        # 9. 天数索引（增强临近日期的关联性）
        df['day_index'] = (df.groupby(df['dateTime'].dt.date).ngroup() + 1)
        
        # 计算特征工程耗时
        feature_time = time.time() - feature_start_time
        print(f"   ✅ 天气特征工程完成，新增 {len(df.columns) - len(df.columns.drop(['hour', 'minute', 'is_weekend', 'week_of_month', 'day_index', 'holiday', 'day_type']))} 个特征")
        print(f"   ⏱️  特征工程耗时: {feature_time:.2f} 秒")
        
        return df


    # ----------------------- 节假日 ----------------------- #
    def _with_holiday_features(self, df: pd.DataFrame, start_day: str, end_day: str) -> pd.DataFrame:
        """添加节假日和工作日类型特征（修正缩进，作为类方法）"""
        # 生成日期范围（包含start_day到end_day的所有日期）
        date_range = pd.date_range(start=start_day, end=end_day)
        date_df = pd.DataFrame({'date': date_range.strftime("%Y-%m-%d")})
        
        # 简单模拟节假日（实际应用中可替换为真实节假日数据）
        date_df['dayofweek'] = pd.to_datetime(date_df['date']).dt.dayofweek
        date_df['day_type'] = date_df['dayofweek'].apply(
            lambda x: 6 if x == 5 else 7 if x == 6 else 1  # 6=周六，7=周日，1=工作日
        )
        date_df['holiday'] = date_df['day_type'].apply(lambda x: 1 if x in [6, 7] else 0)  # 1=节假日，0=工作日
        
        # 合并到原始数据
        df = pd.merge(df, date_df, on='date', how='left')
        return df

    # ----------------------- 月内周 ----------------------- #
    def week_of_month(self, date_series: pd.Series) -> pd.Series:
        """计算日期在当月的第几周（从1开始）（修正缩进，作为类方法）"""
        # 确保输入为datetime类型
        if not pd.api.types.is_datetime64_any_dtype(date_series):
            date_series = pd.to_datetime(date_series)
        
        # 当月第一天
        first_day = date_series.dt.to_period('M').dt.start_time
        # 当月第一周的周一
        first_week_monday = first_day - pd.to_timedelta(first_day.dt.dayofweek, unit='d')
        # 计算当前日期所在周与第一周的差值（以周为单位）
        week_num = ((date_series - first_week_monday) // pd.Timedelta(weeks=1)).astype(int) + 1
        return week_num

    # ----------------------- 划分测试集 ----------------------- #
    def split_train_test_by_ratio(self, merged_df: pd.DataFrame, test_ratio: float = 0.2) -> tuple:
        """按比例划分训练集和测试集（时间序列专用，保留时间顺序）"""
        # 校验必要列
        if 'dateTime' not in merged_df.columns or 'load' not in merged_df.columns:
            raise ValueError("数据集必须包含'dateTime'和'load'列")
        
        # 按时间排序
        merged_df = merged_df.sort_values('dateTime').reset_index(drop=True)
        total_samples = len(merged_df)
        
        # 计算测试集大小（向上取整，确保至少1条）
        test_size = max(1, round(total_samples * test_ratio))
        train_size = total_samples - test_size
        
        # 划分（后test_ratio比例作为测试集）
        train_data = merged_df.iloc[:train_size]
        test_data = merged_df.iloc[train_size:]
        
        # 分离特征和标签
        X_train = train_data.drop(columns=['load', 'dateTime'])
        y_train = train_data['load']
        X_test = test_data.drop(columns=['load', 'dateTime'])
        y_test = test_data['load']
        
        # 打印划分结果
        actual_ratio = len(test_data) / total_samples * 100
        print(f"按{test_ratio*100}%比例划分完成：")
        print(f"训练集：{len(train_data)}条，测试集：{len(test_data)}条，实际测试集占比：{actual_ratio:.1f}%")
        
        self.X_train = X_train  # 保存训练集特征
        self.y_train = y_train  # 保存训练集标签
        self.X_test = X_test    # 保存测试集特征
        self.y_test = y_test    # 保存测试集标签

        return X_train, X_test, y_train, y_test

    # ----------------------- 滑动窗口数据生成（核心修改） ----------------------- #
    def create_sliding_window_data(self, df: pd.DataFrame) -> Tuple[pd.DataFrame, pd.Series, List]:
        """
        创建滑动窗口数据，返回DataFrame格式的特征（含列名）、Series格式的目标值
        """
        # 确保数据已按时间排序
        if not df['dateTime'].is_monotonic_increasing:
            df = df.sort_values('dateTime')
        
        # 提取特征列和目标列
        feature_columns = [col for col in df.columns if col not in ['dateTime', 'load', 'date']]
        target_column = "load"
        
        print(f"使用 {self.history_days} 天历史窗口构建滑动窗口数据")
        print(f"特征列: {len(feature_columns)} 个 ({', '.join(feature_columns[:5])}...等)")
        
        # 统一所有特征列为float64类型（增强容错性）
        try:
            # 对非数值列进行转换，无法转换的用NaN填充后替换为0
            df[feature_columns] = df[feature_columns].apply(
                pd.to_numeric, errors='coerce'
            ).fillna(0.0).astype('float64')
            print(f"特征列已统一转换为float64类型，共 {len(feature_columns)} 列")
        except Exception as e:
            print(f"特征列类型转换警告: {e}，将尝试继续处理")
        
        # 计算每天的数据点数和历史窗口总点数
        if self.load_freq and self.load_freq.endswith('H'):  # 小时频率
            points_per_day = 24 // int(self.load_freq[:-1])
        elif self.load_freq and self.load_freq.endswith('T'):  # 分钟频率
            points_per_day = 1440 // int(self.load_freq[:-1])
        else:
            points_per_day = 1  # 默认每天1个点
            
        history_points = self.history_days * points_per_day
        
        # 确保数据足够构建滑动窗口
        if len(df) <= history_points:
            raise ValueError(f"数据长度({len(df)})不足，无法构建{self.history_days}天的滑动窗口（需要至少{history_points}个点）")
        
        # 生成特征列名（关键修改：明确列名，便于后续模型使用）
        # 只使用历史窗口特征列名（格式：history_时间索引_特征名）
        history_col_names = []
        for time_idx in range(history_points):
            for feat in feature_columns:
                history_col_names.append(f"history_{time_idx}_{feat}")

        # 注意：不包含目标日特征，因为在实际预测时目标日的特征是未知的
        all_col_names = history_col_names
        
        # 构建滑动窗口数据
        samples = []  # 存储所有样本的特征
        targets = []  # 存储目标值
        dates = []    # 存储目标日期
        
        for i in range(len(df) - history_points):
            # 历史窗口（过去history_days天的数据）
            history_window = df.iloc[i:i+history_points]
            
            # 目标日（预测目标对应的日期）
            target_day = df.iloc[i+history_points]
            
            # 构建特征向量（只使用历史特征）
            history_features = history_window[feature_columns].values.flatten()
            sample_features = history_features  # 只使用历史特征
            
            # 添加到数据集
            samples.append(sample_features)
            targets.append(target_day[target_column])
            dates.append(target_day["dateTime"])
        
        # 转换为DataFrame（核心修改：返回DataFrame而非numpy数组）
        X_df = pd.DataFrame(samples, columns=all_col_names)
        y_series = pd.Series(targets, name=target_column)  # 转换为Series，便于对齐
        
        print(f"滑动窗口数据构建完成，共生成 {len(X_df)} 个样本")
        print(f"每个样本包含 {X_df.shape[1]} 个历史特征（{len(history_col_names)}个）")
        
        return X_df, y_series, dates  # 返回DataFrame、Series、日期列表

    # ----------------------- 普通时间序列数据处理 ----------------------- #
    def create_simple_time_series_data(self, df: pd.DataFrame) -> tuple:
        """
        创建普通时间序列数据（不使用滑动窗口）
        适用于LSTM等时间序列模型

        返回:
            (X_df, y_series, dates): 特征DataFrame、目标Series、日期列表
        """
        # 确保数据已按时间排序
        if not df['dateTime'].is_monotonic_increasing:
            df = df.sort_values('dateTime')

        # 提取特征列和目标列
        feature_columns = [col for col in df.columns if col not in ['dateTime', 'load', 'date']]
        target_column = "load"

        print(f"使用普通时间序列数据处理")
        print(f"特征列: {len(feature_columns)} 个 ({', '.join(feature_columns[:5])}...等)")

        # 统一所有特征列为float64类型
        try:
            df[feature_columns] = df[feature_columns].apply(
                pd.to_numeric, errors='coerce'
            ).fillna(0.0).astype('float64')
            print(f"特征列已统一转换为float64类型，共 {len(feature_columns)} 列")
        except Exception as e:
            print(f"特征列类型转换警告: {e}，将尝试继续处理")

        # 分离特征和目标
        X_df = df[feature_columns].copy()
        y_series = df[target_column].copy()
        dates = df['dateTime'].tolist()

        print(f"普通时间序列数据处理完成，共 {len(X_df)} 个样本")
        print(f"每个样本包含 {X_df.shape[1]} 个特征")

        return X_df, y_series, dates

    def create_enhanced_time_series_data(self, df: pd.DataFrame) -> tuple:
        """
        创建增强的时间序列数据（添加更多特征工程）
        """
        # 基础处理
        X_df, y_series, dates = self.create_simple_time_series_data(df)

        # 添加滞后特征（基于数据频率，假设15分钟频率）
        lags = [1, 2, 4, 8, 24, 48, 96]  # 15分钟到1天的滞后
        for lag in lags:
            if lag < len(y_series):
                X_df[f'load_lag_{lag}'] = y_series.shift(lag)

        # 添加滚动统计特征
        windows = [4, 8, 24, 96]  # 1小时到1天的窗口
        for window in windows:
            if window < len(y_series):
                X_df[f'load_rolling_mean_{window}'] = y_series.rolling(window).mean()
                X_df[f'load_rolling_std_{window}'] = y_series.rolling(window).std()
                X_df[f'load_rolling_max_{window}'] = y_series.rolling(window).max()
                X_df[f'load_rolling_min_{window}'] = y_series.rolling(window).min()

        # 添加差分特征
        X_df['load_diff_1'] = y_series.diff(1)
        X_df['load_diff_24'] = y_series.diff(24)  # 6小时差分（15分钟频率下24=6小时）
        X_df['load_diff_96'] = y_series.diff(96)  # 1天差分

        # 添加温度相关的交互特征（使用实际温度列名t_2m）
        if 't_2m' in X_df.columns:
            X_df['temp_load_interaction'] = X_df['t_2m'] * y_series.shift(1)  # 温度与滞后1期负荷的交互
            X_df['temp_squared'] = X_df['t_2m'] **2  # 温度平方项（捕捉非线性关系）
            X_df['temp_rolling_mean_24'] = X_df['t_2m'].rolling(24).mean()  # 温度滚动均值

        # 填充缺失值（前向填充+后向填充+0填充）
        X_df = X_df.fillna(method='ffill').fillna(method='bfill').fillna(0)

        print(f"增强特征工程完成，特征数从 {len(X_df.columns) - len(lags) - 4*len(windows) - 3 - (3 if 't_2m' in X_df.columns else 0)} 增加到 {X_df.shape[1]}")

        return X_df, y_series, dates