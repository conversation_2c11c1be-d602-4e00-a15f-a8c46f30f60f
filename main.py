import pandas as pd
import matplotlib.pyplot as plt
import yaml
import numpy as np
from time_series_plot import TimeSeriesPlotter
# import os  # 暂时不需要


# 导入自定义模块
from data_loader import DataPreprocessor
from tab_pfn import TabPfn
from lstm_model import LSTMModel  # LSTM模型
# from model_evaluator import ModelEvaluator  # 暂时不需要
from model_compare import ModelManager

# 可选导入XGBoost模型
try:
    from xgboost_model import XGBoostModel
    XGBOOST_AVAILABLE = True
except ImportError:
    XGBOOST_AVAILABLE = False
    print("⚠️ XGBoost未安装，XGBoost模型将被禁用")

# ======================== 在这里集中定义客户信息等配置 ========================
customer_code = "潞城市三顺溶剂厂"
start_date = "2025-05-01"
end_date = "2025-07-17"
history_days = 7  # 历史天数（从14天减少到7天以提高效率）

def main():
    global customer_code, start_date, end_date, history_days

    # -------------------- 加载配置 --------------------
    try:
        with open("config.yaml", "r", encoding="utf-8") as f:
            config = yaml.safe_load(f)
        print("✅ 配置加载成功")
    except Exception as e:
        print(f"配置加载失败: {e}")
        # 使用默认配置
        config = {
            "use_tabpfn": True,
            "tabpfn_params": {
                "device": "auto",
                "random_state": 80
            },
            "test_size": 0.2,
            "metric_to_compare": "rmse",
            "save_predictions": True,
            "plot_predictions": True,
            "future_days": 7,
            "output_dir": "results"
        }
    
    # -------------------- 数据预处理 --------------------
    print("🔄 开始初始化数据预处理器...")
    preprocessor = DataPreprocessor(
        start_date=start_date,
        end_date=end_date,
        excel_file_path='山西零售用户负荷.xlsx',
        weather_csv_path="weather_山西.csv",
        history_days=history_days
    )

    # -----------加载数据----------- #
    print("🔄 开始加载负荷数据...")
    df_real_load = preprocessor.load_user_load(customer_code=customer_code)
    print(f"负荷数据: {len(df_real_load)} 条，时间范围: {df_real_load['dateTime'].min()} 到 {df_real_load['dateTime'].max()}")

    df_weather = preprocessor.load_weather_data()
    print(f"天气数据: {len(df_weather)} 条，时间范围: {df_weather['t_datetime_cst'].min()} 到 {df_weather['t_datetime_cst'].max()}")

    # -------------------- 特征工程 --------------------
    df_weather_with_features = preprocessor.process_weather_features(df_weather)
    df_merged = pd.merge(
        df_real_load,
        df_weather_with_features,
        on="dateTime",
        how="inner"
    )

    # -------------------- 数据处理方式选择 --------------------
    use_sliding_window = config.get("use_sliding_window", False)
    use_enhanced_features = config.get("use_enhanced_features", False)
    data_method = "滑动窗口" if use_sliding_window else ("增强时间序列" if use_enhanced_features else "普通时间序列")
    print(f"\n【数据处理方式】: {data_method}")

    # 提取原始特征列
    original_feature_cols = [col for col in df_merged.columns if col not in ['dateTime', 'load', 'date']]
    n_original_features = len(original_feature_cols)
    print(f"原始特征数: {n_original_features}")
    print(f"特征示例: {', '.join(original_feature_cols[:5])}...")

    if use_sliding_window:
        # -------------------- 滑动窗口数据处理 --------------------
        print(f"\n【使用滑动窗口数据处理】")

        # 计算数据频率和每天点数
        load_freq = preprocessor.load_freq
        if load_freq and load_freq.endswith('H'):
            points_per_day = 24 // int(load_freq[:-1])
        elif load_freq and load_freq.endswith('T'):
            points_per_day = 1440 // int(load_freq[:-1])  # 1440 = 24*60
        else:
            points_per_day = 1
        print(f"数据频率: {load_freq}，每天数据点数: {points_per_day}")

        # 计算理论滑动窗口特征数
        window_steps = history_days * points_per_day
        theoretical_features = window_steps * n_original_features
        print(f"滑动窗口参数: 历史天数={history_days}, 窗口总步数={window_steps}")
        print(f"理论特征数: {theoretical_features}")

        # 生成滑动窗口数据
        X_df, y_series, dates = preprocessor.create_sliding_window_data(df_merged)
        actual_features = X_df.shape[1]
        print(f"实际滑动窗口特征数: {actual_features}")

        # 特征数不匹配时的警告
        if actual_features != theoretical_features:
            print(f"⚠️ 【特征不匹配警告】理论={theoretical_features}, 实际={actual_features}")
            print(f"   可能原因: 滑动窗口实现错误 / 数据频率识别错误 / 特征工程引入额外特征")

    elif use_enhanced_features:
        # -------------------- 增强时间序列数据处理 --------------------
        print(f"\n【使用增强时间序列数据处理】")
        X_df, y_series, dates = preprocessor.create_enhanced_time_series_data(df_merged)

    else:
        # -------------------- 普通时间序列数据处理 --------------------
        print(f"\n【使用普通时间序列数据处理】")
        X_df, y_series, dates = preprocessor.create_simple_time_series_data(df_merged)

    # 划分训练集和测试集
    test_size = config.get("test_size", 0.2) # 从配置对象config中获取测试集的比例，如果配置中没有指定，则使用默认值 0.2
    # 计算测试集的样本数量，并调整为96的倍数
    raw_test_samples = int(len(X_df) * test_size)
    test_samples = max(96, (raw_test_samples // 96) * 96)  # 取不超过raw_test_samples的最大96的倍数，至少为96
    X_train = X_df.iloc[:-test_samples] # 切片操作
    X_test = X_df.iloc[-test_samples:]
    y_train = y_series.iloc[:-test_samples]
    y_test = y_series.iloc[-test_samples:]
    dates_train, dates_test = dates[:-test_samples], dates[-test_samples:]

    print(f"\n训练集: {len(X_train)} 样本，测试集: {len(X_test)} 样本")
    print(f"特征维度: {X_train.shape[1]}")

    # 输出训练集和测试集的日期范围
    if dates_train and isinstance(dates_train[0], (str, pd.Timestamp)):
        print(f"训练集日期范围: {dates_train[0]} 到 {dates_train[-1]}")
    if dates_test and isinstance(dates_test[0], (str, pd.Timestamp)):
        print(f"测试集日期范围: {dates_test[0]} 到 {dates_test[-1]}")

    # -------------- 创建模型实例 -------------- #
    print(f"\n=== 开始滚动预测 ===")
    model_manager = ModelManager(preprocessor, X_train, y_train, X_test, y_test, dates_train, dates_test)

    # 添加TABPFN模型（开启自动调整）
    if config.get("use_tabpfn", False):
        tabpfn_model = TabPfn(
            device = config.get("tabpfn_params", {}).get("device", "auto"),
            random_state = config.get("tabpfn_params", {}).get("random_state", 80),
            history_days = history_days,
        )

        # 初始化模型（传入原始特征列）
        if not df_merged.empty and 'dateTime' in df_merged.columns and len(df_merged) >= 2:
            tabpfn_model.init_from_data(
                df_merged,
                time_col = 'dateTime',
                feature_cols = original_feature_cols
            )
        else:
            raise ValueError("无法初始化TabPFN模型：数据不足或格式错误")

        model_manager.add_model("TabPfn", tabpfn_model)

    # 添加LSTM模型
    if config.get("use_lstm", True):  # 新增LSTM的配置开关
        lstm_params = config.get("lstm_params", {
            "sequence_length": 96,  # 1天的数据点数（15分钟间隔）
            "hidden_size": 64,
            "num_layers": 2,
            "dropout": 0.2,
            "learning_rate": 0.001,
            "epochs": 50,  # 减少训练轮数以节省时间
            "batch_size": 32,
            "device": "auto",
            "early_stopping_patience": 10
        })

        # 实例化LSTM模型并添加到管理器
        lstm_model = LSTMModel(
            sequence_length=lstm_params["sequence_length"],
            hidden_size=lstm_params["hidden_size"],
            num_layers=lstm_params["num_layers"],
            dropout=lstm_params["dropout"],
            learning_rate=lstm_params["learning_rate"],
            epochs=lstm_params["epochs"],
            batch_size=lstm_params["batch_size"],
            device=lstm_params["device"],
            early_stopping_patience=lstm_params["early_stopping_patience"]
        )

        model_manager.add_model("LSTM", lstm_model)

    # 添加XGBoost模型
    if config.get("use_xgboost", False) and XGBOOST_AVAILABLE:  # 检查是否可用
        xgboost_params = config.get("xgboost_params", {
            "n_estimators": 500,
            "max_depth": 6,
            "learning_rate": 0.1,
            "subsample": 0.8,
            "random_state": 42,
            "feature_engineering": True,
            "lag_features": True
        })

        # 实例化梯度提升模型并添加到管理器
        xgboost_model = XGBoostModel(
            n_estimators=xgboost_params["n_estimators"],
            max_depth=xgboost_params["max_depth"],
            learning_rate=xgboost_params["learning_rate"],
            subsample=xgboost_params["subsample"],
            random_state=xgboost_params["random_state"],
            feature_engineering=xgboost_params["feature_engineering"],
            lag_features=xgboost_params["lag_features"]
        )

        model_manager.add_model("XGBoost", xgboost_model)

    # -------------------- 训练与评估 --------------------    
    print("\n开始训练所有模型...")
    model_manager.train_all_models()

    print("\n开始评估所有模型...")
    model_manager.evaluate_all_models()

    # 获取最佳模型
    metric_to_compare = config.get("metric_to_compare", "rmse")
    best_model_name, best_metrics = model_manager.get_best_model(metric_to_compare)

    print(f"\n最佳模型: {best_model_name}")
    if best_metrics and metric_to_compare in best_metrics:
        print(f"评估指标 ({metric_to_compare}): {best_metrics[metric_to_compare]:.4f}")
    else:
        print(f"警告：未获取到 {metric_to_compare} 指标数据")

    # 打印评估结果
    print("\n所有模型的评估结果:")
    for name, metrics in model_manager.evaluation_results.items():
        print(f"- {name}: RMSE = {metrics['rmse']:.4f}, MAE = {metrics['mae']:.4f}")

    # -------------------- 保存测试集预测结果 --------------------
    if best_model_name in model_manager.models:
        best_model = model_manager.models[best_model_name]

        # 获取测试集预测结果
        test_predictions = best_model.predict(X_test)['value']
        
        # 获取训练集预测结果
        train_predictions = best_model.predict(X_train)['value']

        # 处理LSTM等序列模型预测结果数量不匹配的问题
        if len(test_predictions) != len(dates_test):
            print(f"⚠️ 预测结果数量({len(test_predictions)})与测试集日期({len(dates_test)})不匹配")
            if len(test_predictions) < len(dates_test):
                # 使用对应的日期
                dates_test_aligned = dates_test[-len(test_predictions):]
                y_test_aligned = y_test.iloc[-len(test_predictions):] if hasattr(y_test, 'iloc') else y_test[-len(test_predictions):]
                print(f"📊 使用最后{len(test_predictions)}个样本保存结果")
            else:
                # 截取预测结果
                test_predictions = test_predictions[:len(dates_test)]
                dates_test_aligned = dates_test
                y_test_aligned = y_test
        else:
            dates_test_aligned = dates_test
            y_test_aligned = y_test

        # 处理训练集预测结果数量不匹配（新增）
        if len(train_predictions) != len(dates_train):
            print(f"⚠️ 训练集预测结果数量({len(train_predictions)})与训练集日期({len(dates_train)})不匹配")
            if len(train_predictions) < len(dates_train):
                dates_train_aligned = dates_train[-len(train_predictions):]
                y_train_aligned = y_train.iloc[-len(train_predictions):] if hasattr(y_train, 'iloc') else y_train[-len(train_predictions):]
            else:
                train_predictions = train_predictions[:len(dates_train)]
                dates_train_aligned = dates_train
                y_train_aligned = y_train
        else:
            dates_train_aligned = dates_train
            y_train_aligned = y_train

        # 创建测试集结果DataFrame
        test_results_df = pd.DataFrame({
            'datetime': dates_test_aligned,
            'predicted_load': test_predictions
        })

        # 保存到CSV文件
        test_csv_path = f"{best_model_name}_test_predictions.csv"
        test_results_df.to_csv(test_csv_path, index=False)
        print(f"\n测试集预测结果已保存至: {test_csv_path}")

        # -------------------- 使用TimeSeriesPlotter类绘图 --------------------
        # 从配置中获取绘图参数
        plot_config = config.get("plot_config", {})
        splitting_number = plot_config.get("splitting_number", 3)

        # 创建绘图器实例（只需要传入分段数）
        plotter = TimeSeriesPlotter(splitting_number)

        # 1. 完整时序图（训练集+测试集）
        full_plot_path = f"{best_model_name}_full_time_series.png"
        plotter.plot_full_time_series(
            dates_train_aligned, y_train_aligned, train_predictions,
            dates_test_aligned, y_test_aligned, test_predictions,
            best_model_name, full_plot_path
        )

        # 2. 分段时序图
        split_plot_path = f"{best_model_name}_split_time_series_{splitting_number}segments.png"
        plotter.plot_split_time_series(
            dates_train_aligned, y_train_aligned, train_predictions,
            dates_test_aligned, y_test_aligned, test_predictions,
            best_model_name, split_plot_path
        )

        # 3. 单独测试集对比图（保留原有）
        plt.figure(figsize=(15, 8))

        # 绘制测试集真实值
        plt.plot(dates_test_aligned, y_test_aligned, label="真实值", color="blue", linewidth=2, marker='o', markersize=3)

        # 绘制测试集预测值
        plt.plot(dates_test_aligned, test_predictions, label="预测值", color="red", linewidth=2, linestyle="--", marker='s', markersize=3)

        # 设置图表属性
        plt.title(f"{best_model_name} 模型测试集预测对比", fontsize=16, fontweight='bold')
        plt.xlabel("时间", fontsize=12)
        plt.ylabel("负荷值", fontsize=12)
        plt.legend(fontsize=12)
        plt.grid(True, alpha=0.3)
        plt.xticks(rotation=45)
        plt.tight_layout()

        # 保存测试集对比图
        test_plot_path = f"{best_model_name}_test_comparison.png"
        plt.savefig(test_plot_path, dpi=300, bbox_inches='tight')
        plt.close()
        print(f"测试集对比图已保存至: {test_plot_path}")

        # 计算并显示一些统计信息
        import numpy as np
        y_test_array = np.array(y_test_aligned)
        test_pred_array = np.array(test_predictions)

        mae = np.mean(np.abs(y_test_array - test_pred_array))
        rmse = np.sqrt(np.mean((y_test_array - test_pred_array) ** 2))

        # 避免除零错误
        nonzero_mask = y_test_array != 0
        if np.sum(nonzero_mask) > 0:
            mape = np.mean(np.abs((y_test_array[nonzero_mask] - test_pred_array[nonzero_mask]) / y_test_array[nonzero_mask]) * 100)
        else:
            mape = float('inf')

        print(f"\n测试集详细评估指标:")
        print(f"- MAE (平均绝对误差): {mae:.4f}")
        print(f"- RMSE (均方根误差): {rmse:.4f}")
        print(f"- MAPE (平均绝对百分比误差): {mape:.2f}%" if mape != float('inf') else "- MAPE: 无法计算（存在零值）")


if __name__ == "__main__":
    main()